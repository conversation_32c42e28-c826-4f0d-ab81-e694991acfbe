import redis

from db.mysql import UsingMysql

with UsingMysql() as um:
    privacy = um.fetch_decrypt("select value from info_db where index_information = 'redis' ",
                               None, 'value')

pool = redis.ConnectionPool.from_url(url=privacy + '/2',
                                     decode_responses=True,
                                     socket_connect_timeout=3,
                                     socket_timeout=5,
                                     socket_keepalive=True,
                                     retry_on_timeout=True)
redis_client = redis.Redis(connection_pool=pool)

# print(redis_client.set("privacy_bind_token", "123456", ex=10)) 返回是否设置成功的bool，ex为过期时间（秒）
# print(redis_client.get("privacy_bind_token"))
