# 连接本地SQLite数据库
import sqlite3
import regex as re

def regexp(pattern, item):
    return re.search(pattern, item) is not None  # 明确参数顺序

class UsingSQLite(object):  # 使用 with 的方式来优化代码

    def __init__(self, name, commit=True):
        """
        :param commit: 是否在最后提交事务(设置为False的时候方便单元测试)
        """
        self._commit = commit
        self._name = name

    def __enter__(self):
        # 连接到SQLite数据库
        self._conn = sqlite3.connect(f'app/resource/db/{self._name}.db')
        self._conn.create_function('REGEXP', 2, regexp)  # 开启正则
        self._cursor = self._conn.cursor()
        return self

    def __exit__(self, *exc_info):
        # 提交事务
        if self._commit:
            self._conn.commit()
        # 在退出的时候自动关闭连接和cursor
        self._cursor.close()
        self._conn.close()

    @property
    def cursor(self):
        return self._cursor

    # ========= 一系列封装的业务方法
    def fetch_all(self, sql):
        self.cursor.execute(sql)
        header = [desc[0] for desc in self.cursor.description]  # 获取列名
        content = self.cursor.fetchall()  # 获取数据
        return header, content  # 返回列名和数据

    def execute(self, sql):
        self.cursor.execute(sql)
        return self.cursor.rowcount


# 示例使用
if __name__ == "__main__":
    with UsingSQLite('DRG') as us:
        header, content = us.fetch_all("SELECT * FROM ADRG")
        print(header)
        print(content)
