# 连接mysql数据库

from timeit import default_timer
from urllib.parse import quote

import pymysql
from dbutils.pooled_db import PooledDB
from sqlalchemy import create_engine

from function.methods_general import decrypt


class DMysqlConfig:
    def __init__(self, host, db, user, password, port=3306):
        self.host = host  # 数据库ip地址
        self.port = port  # 数据库端口
        self.db = db  # 库名
        self.user = user  # 用户名
        self.password = password  # 密码

        self.charset = 'utf8mb4'  # 不能是 utf-8  # 字符编码
        self.minCached = 1  # 连接池中空闲连接的初始数量
        self.maxCached = 20  # 连接池中空闲连接的最大数量
        self.maxShared = 10  # 共享连接的最大数量
        self.maxConnection = 50  # 创建连接池的最大数量

        self.blocking = True  # 超过最大连接数量时候的表现，为True等待连接数量下降，为false直接报错处理
        self.maxUsage = 100  # 单个连接的最大重复使用次数
        self.setSession = None  # 可用于准备的SQL命令的可选列表会话，例如[“将日期样式设置为…”，“设置时区…”]
        self.reset = True  # 重置：当连接返回到池中时，应如何重置连接（False或None用于回滚以begin（）开始的事务，确实，为了安全起见，总是会发出回滚）


# ---- 用连接池来返回数据库连接
class DMysqlPoolConn:
    __pool = None

    def __init__(self, config):
        if not self.__pool:
            self.__class__.__pool = PooledDB(creator=pymysql,
                                             maxconnections=config.maxConnection,
                                             mincached=config.minCached,
                                             maxcached=config.maxCached,
                                             maxshared=config.maxShared,
                                             blocking=config.blocking,
                                             maxusage=config.maxUsage,
                                             setsession=config.setSession,
                                             charset=config.charset,
                                             host=config.host,
                                             port=config.port,
                                             database=config.db,
                                             user=config.user,
                                             password=config.password,
                                             )

    def get_conn(self):
        return self.__pool.connection()


# ========== 在程序的开始初始化一个连接池（在程序运行周期内不管调用几次只会运行一次）
try:
    from register import K, USE, PW
except ImportError as e:
    print(f"导入失败: {e}")
dip_params = {
    'host': '***************',
    'port': 33060,
    'user': decrypt(K, USE),
    'password': decrypt(K, PW),
    'db': 'chronic_disease_Platform',
}
db_config = DMysqlConfig(**dip_params)
g_pool_connection = DMysqlPoolConn(db_config)

# dataframe的to_sql使用
engine = create_engine(
    f'mysql+pymysql://{db_config.user}:{quote(db_config.password)}@{db_config.host}:{db_config.port}/{db_config.db}?charset=utf8mb4'
)


class UsingMysql(object):  # 使用 with 的方式来优化代码

    def __init__(self, commit=True, log_time=False, log_label='mysql总用时'):
        """
        :param commit: 是否在最后提交事务(设置为False的时候方便单元测试)
        :param log_time:  是否打印程序运行总时间
        :param log_label:  自定义log的文字
        """
        self._log_time = log_time
        self._commit = commit
        self._log_label = log_label

    def __enter__(self):
        # 如果需要记录时间
        if self._log_time is True:
            self._start = default_timer()

        # 从连接池获取数据库连接
        conn = g_pool_connection.get_conn()
        cursor = conn.cursor(pymysql.cursors.DictCursor)  # 使用 cursor() 方法创建一个 dict 格式的游标对象 cursor
        conn.autocommit = False

        self._conn = conn
        self._cursor = cursor
        return self

    def __exit__(self, *exc_info):
        # 提交事务
        if self._commit:
            self._conn.commit()
        # 在退出的时候自动关闭连接和cursor
        self._cursor.close()
        self._conn.close()

        if self._log_time is True:
            diff = default_timer() - self._start
            print('-- %s: %.6f 秒' % (self._log_label, diff))

    # ========= 一系列封装的业务方法
    def fetch_all(self, sql, params=None):
        self.cursor.execute(sql, params)
        result = self.cursor.fetchall()
        if result:
            header = list(result[0].keys())
            content = [list(row.values()) for row in result]
            return header, content
        else:
            return None, None

    def execute(self, sql):
        self.cursor.execute(sql)
        return self.cursor.rowcount

    def fetch_decrypt(self, sql, params, count_key):
        self.cursor.execute("select value from info_db where index_information = 'key' ", None)
        data = self.cursor.fetchone()
        key_m = data['value']
        key = decrypt(K, key_m)  # 二次解密Key

        self.cursor.execute(sql, params)
        data = self.cursor.fetchone()
        data_m = data[count_key]
        data = decrypt(key, data_m)
        return data

    @property
    def cursor(self):
        return self._cursor
