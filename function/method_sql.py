from db.execute import db


def get_sql(sql_id: str, replacements: list) -> str:
    """
    将数据库中未替换的sql构建为最终的sql
    :param sql_id: info_sql中的id名：’antibiotic‘
    :param replacements: 把sql中的[[{i}]]替换的列表：[time_start, time_end]
    """
    sql_content = db('sql_content', 'info_sql', {'id': sql_id}, 'mysql')
    for ids, data in {f'[[{i}]]': value for i, value in enumerate(replacements, start=0)}.items():  # 构建最终sql语句
        sql_content = sql_content.replace(ids, data)
    return sql_content


def query_phone(code: str):
    """ sms中依据工号查询电话号码 """
    return db('TEL', 'yzzxyy.info_phone', {'CODE': code}, 'mysql')


def query_name(code: str):
    """ sms中依据工号查询电话号码 """
    return db('name', 'yzzxyy.info_user', {'id': code}, 'mysql')


def query_openid(code: str) -> str:
    """ 查询工号对应的微信OpenID """
    return db('wx_id', 'yzzxyy.info_user', {'id': code}, 'mysql')
