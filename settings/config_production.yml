# 生产环境配置
app:
  name: "yongzhou_worker"
  version: "1.0.0"
  debug: false
  environment: "production"



celery:
  broker_db: 2
  result_backend_db: 3
  task_serializer: "json"
  result_serializer: "json"
  accept_content: ["json"]
  timezone: "Asia/Shanghai"
  enable_utc: true
  
  # 生产环境性能配置
  worker_prefetch_multiplier: 4
  task_acks_late: true
  worker_max_tasks_per_child: 1000
  worker_max_memory_per_child: 209715200  # 200MB
  task_queue_max_priority: 10
  task_default_priority: 5
  
  # 错误处理配置
  task_acks_on_failure_or_timeout: false
  task_reject_on_worker_lost: true
  result_backend_always_retry: true
  result_backend_max_retries: 10
  result_backend_thread_safe: true
  result_expires: 3600  # 1小时

log:
  level: "WARNING"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "/var/log/yongzhou_worker/worker.log"
  max_file_size: 52428800  # 50MB
  backup_count: 10
