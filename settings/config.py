"""
配置文件 - 应用配置
支持环境变量和配置文件
"""
import os
import yaml
from pathlib import Path
from typing import Optional
from pydantic import BaseModel


class CeleryConfig(BaseModel):
    """Celery配置"""
    broker_db: int = 2
    result_backend_db: int = 3
    task_serializer: str = "json"
    result_serializer: str = "json"
    accept_content: list = ["json"]
    timezone: str = "Asia/Shanghai"
    enable_utc: bool = True

    # 性能配置
    worker_prefetch_multiplier: int = 1
    task_acks_late: bool = True
    worker_max_tasks_per_child: int = 100
    worker_max_memory_per_child: int = 104857600  # 100MB
    task_queue_max_priority: int = 10
    task_default_priority: int = 5

    # 错误处理配置
    task_acks_on_failure_or_timeout: bool = False
    task_reject_on_worker_lost: bool = True
    result_backend_always_retry: bool = True
    result_backend_max_retries: int = 10
    result_backend_thread_safe: bool = True
    result_expires: int = 3600  # 1小时


class LogConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5


class AppConfig(BaseModel):
    """应用配置"""
    name: str = "yongzhou_worker"
    version: str = "1.0.0"
    debug: bool = True
    environment: str = "development"


class Settings(BaseModel):
    """主配置类"""
    app: AppConfig = AppConfig()
    celery: CeleryConfig = CeleryConfig()
    log: LogConfig = LogConfig()


def load_config_from_file(config_path: str) -> dict:
    """从配置文件加载配置"""
    config_file = Path(config_path)
    if not config_file.exists():
        return {}

    with open(config_file, 'r', encoding='utf-8') as f:
        if config_file.suffix.lower() in ['.yml', '.yaml']:
            return yaml.safe_load(f) or {}
        else:
            # 可以扩展支持其他格式
            return {}


def load_settings() -> Settings:
    """加载配置"""
    # 默认配置
    config_data = {}

    # 尝试从配置文件加载
    config_files = [
        'config.yml',
        'config.yaml',
        f'config_{os.getenv("ENV", "development")}.yml',
        f'config_{os.getenv("ENV", "development")}.yaml'
    ]

    for config_file in config_files:
        file_config = load_config_from_file(config_file)
        if file_config:
            config_data.update(file_config)
            break

    # 环境变量覆盖
    env_overrides = {
        'app': {
            'debug': os.getenv('DEBUG', 'true').lower() == 'true',
            'environment': os.getenv('ENV', 'development'),
        }
    }

    # 移除None值
    for section, values in env_overrides.items():
        filtered_values = {k: v for k, v in values.items() if v is not None}
        if filtered_values:
            if section not in config_data:
                config_data[section] = {}
            config_data[section].update(filtered_values)

    return Settings(**config_data)


# 全局配置实例
settings = load_settings()
