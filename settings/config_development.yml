# 开发环境配置
app:
  name: "yongzhou_worker"
  version: "1.0.0"
  debug: true
  environment: "development"



celery:
  broker_db: 2
  result_backend_db: 3
  task_serializer: "json"
  result_serializer: "json"
  accept_content: ["json"]
  timezone: "Asia/Shanghai"
  enable_utc: true
  
  # 性能配置
  worker_prefetch_multiplier: 1
  task_acks_late: true
  worker_max_tasks_per_child: 100
  worker_max_memory_per_child: 104857600  # 100MB
  task_queue_max_priority: 10
  task_default_priority: 5
  
  # 错误处理配置
  task_acks_on_failure_or_timeout: false
  task_reject_on_worker_lost: true
  result_backend_always_retry: true
  result_backend_max_retries: 10
  result_backend_thread_safe: true
  result_expires: 3600  # 1小时

log:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/worker.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
