"""
数据库工厂模块
提供统一的数据库实例创建和管理
"""
from typing import Dict, Any, Optional, Union
import logging
from enum import Enum

from .config import MySQLConfig, OracleConfig, SQLiteConfig, RedisConfig
from .connections import MySQLConnection, OracleConnection, SQLiteConnection, RedisConnection
from .operations import BaseOperations, TransactionManager

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """数据库类型枚举"""
    MYSQL = "mysql"
    ORACLE = "oracle"
    SQLITE = "sqlite"
    REDIS = "redis"
    DRG = "DRG"  # 兼容原有的DRG类型


class DatabaseFactory:
    """数据库工厂类"""
    
    _instances: Dict[str, Any] = {}
    _connections: Dict[str, Any] = {}
    
    @classmethod
    def create_connection(cls, 
                         db_type: Union[str, DatabaseType], 
                         config: Optional[Dict[str, Any]] = None,
                         instance_name: str = "default") -> Any:
        """
        创建数据库连接
        
        Args:
            db_type: 数据库类型
            config: 配置参数
            instance_name: 实例名称，用于连接复用
            
        Returns:
            数据库连接实例
        """
        if isinstance(db_type, str):
            db_type = DatabaseType(db_type.lower())
        
        cache_key = f"{db_type.value}_{instance_name}"
        
        # 检查是否已有缓存的连接
        if cache_key in cls._connections:
            return cls._connections[cache_key]
        
        try:
            if db_type == DatabaseType.MYSQL:
                connection = cls._create_mysql_connection(config)
            elif db_type == DatabaseType.ORACLE:
                connection = cls._create_oracle_connection(config)
            elif db_type in [DatabaseType.SQLITE, DatabaseType.DRG]:
                connection = cls._create_sqlite_connection(config, db_type)
            elif db_type == DatabaseType.REDIS:
                connection = cls._create_redis_connection(config)
            else:
                raise ValueError(f"不支持的数据库类型: {db_type}")
            
            # 缓存连接
            cls._connections[cache_key] = connection
            logger.info(f"创建 {db_type.value} 连接成功: {instance_name}")
            
            return connection
            
        except Exception as e:
            logger.error(f"创建 {db_type.value} 连接失败: {e}")
            raise
    
    @classmethod
    def _create_mysql_connection(cls, config: Optional[Dict[str, Any]]) -> MySQLConnection:
        """创建MySQL连接"""
        if config:
            mysql_config = MySQLConfig.from_dict(config)
        else:
            # 使用默认配置（从register模块）
            mysql_config = MySQLConfig.from_register()
        
        return MySQLConnection(mysql_config)
    
    @classmethod
    def _create_oracle_connection(cls, config: Optional[Dict[str, Any]]) -> OracleConnection:
        """创建Oracle连接"""
        if config:
            oracle_config = OracleConfig.from_dict(config)
        else:
            # 使用默认方式（从MySQL解密获取）
            mysql_conn = cls.create_connection(DatabaseType.MYSQL)
            return OracleConnection.from_mysql_decrypt(mysql_conn)
        
        return OracleConnection(oracle_config)
    
    @classmethod
    def _create_sqlite_connection(cls, 
                                config: Optional[Dict[str, Any]], 
                                db_type: DatabaseType) -> SQLiteConnection:
        """创建SQLite连接"""
        if config:
            sqlite_config = SQLiteConfig.from_dict(config)
        else:
            # 使用默认配置
            db_name = "DRG" if db_type == DatabaseType.DRG else "default"
            sqlite_config = SQLiteConfig.from_name(db_name)
        
        return SQLiteConnection(sqlite_config)
    
    @classmethod
    def _create_redis_connection(cls, config: Optional[Dict[str, Any]]) -> RedisConnection:
        """创建Redis连接"""
        if config:
            redis_config = RedisConfig.from_dict(config)
        else:
            # 使用默认方式（从MySQL解密获取）
            mysql_conn = cls.create_connection(DatabaseType.MYSQL)
            return RedisConnection.from_mysql_decrypt(mysql_conn)
        
        return RedisConnection(redis_config)
    
    @classmethod
    def create_operations(cls, 
                         db_type: Union[str, DatabaseType],
                         config: Optional[Dict[str, Any]] = None,
                         instance_name: str = "default") -> BaseOperations:
        """
        创建数据库操作实例
        
        Args:
            db_type: 数据库类型
            config: 配置参数
            instance_name: 实例名称
            
        Returns:
            数据库操作实例
        """
        connection = cls.create_connection(db_type, config, instance_name)
        return BaseOperations(connection)
    
    @classmethod
    def create_transaction_manager(cls,
                                 db_type: Union[str, DatabaseType],
                                 config: Optional[Dict[str, Any]] = None,
                                 instance_name: str = "default") -> TransactionManager:
        """
        创建事务管理器
        
        Args:
            db_type: 数据库类型
            config: 配置参数
            instance_name: 实例名称
            
        Returns:
            事务管理器实例
        """
        connection = cls.create_connection(db_type, config, instance_name)
        return TransactionManager(connection)
    
    @classmethod
    def get_connection(cls, 
                      db_type: Union[str, DatabaseType],
                      instance_name: str = "default") -> Optional[Any]:
        """
        获取已创建的连接
        
        Args:
            db_type: 数据库类型
            instance_name: 实例名称
            
        Returns:
            数据库连接实例或None
        """
        if isinstance(db_type, str):
            db_type = DatabaseType(db_type.lower())
        
        cache_key = f"{db_type.value}_{instance_name}"
        return cls._connections.get(cache_key)
    
    @classmethod
    def close_connection(cls, 
                        db_type: Union[str, DatabaseType],
                        instance_name: str = "default"):
        """
        关闭指定连接
        
        Args:
            db_type: 数据库类型
            instance_name: 实例名称
        """
        if isinstance(db_type, str):
            db_type = DatabaseType(db_type.lower())
        
        cache_key = f"{db_type.value}_{instance_name}"
        if cache_key in cls._connections:
            del cls._connections[cache_key]
            logger.info(f"关闭 {db_type.value} 连接: {instance_name}")
    
    @classmethod
    def close_all_connections(cls):
        """关闭所有连接"""
        cls._connections.clear()
        logger.info("关闭所有数据库连接")
    
    @classmethod
    def list_connections(cls) -> Dict[str, str]:
        """列出所有活跃连接"""
        return {key: type(conn).__name__ for key, conn in cls._connections.items()}


# 便捷函数
def get_mysql_connection(config: Optional[Dict[str, Any]] = None, 
                        instance_name: str = "default") -> MySQLConnection:
    """获取MySQL连接"""
    return DatabaseFactory.create_connection(DatabaseType.MYSQL, config, instance_name)


def get_oracle_connection(config: Optional[Dict[str, Any]] = None,
                         instance_name: str = "default") -> OracleConnection:
    """获取Oracle连接"""
    return DatabaseFactory.create_connection(DatabaseType.ORACLE, config, instance_name)


def get_sqlite_connection(database_name: str = "default",
                         instance_name: str = "default") -> SQLiteConnection:
    """获取SQLite连接"""
    config = {"database_name": database_name}
    return DatabaseFactory.create_connection(DatabaseType.SQLITE, config, instance_name)


def get_redis_connection(config: Optional[Dict[str, Any]] = None,
                        instance_name: str = "default") -> RedisConnection:
    """获取Redis连接"""
    return DatabaseFactory.create_connection(DatabaseType.REDIS, config, instance_name)
