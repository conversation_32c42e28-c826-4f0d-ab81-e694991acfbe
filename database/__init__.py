"""
重构后的数据库模块
提供简洁、统一的数据库操作接口
"""
from typing import Union, List, Dict, Any, Optional
import pandas as pd
import logging

from .factory import DatabaseFactory, DatabaseType
from .operations import BaseOperations, QueryBuilder, TransactionManager
from .config import MySQLConfig, OracleConfig, SQLiteConfig, RedisConfig
from .connections import MySQLConnection, OracleConnection, SQLiteConnection, RedisConnection

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导出主要类和函数
__all__ = [
    # 主要接口
    'db', 'query', 'execute', 'transaction',
    # 工厂类
    'DatabaseFactory', 'DatabaseType',
    # 配置类
    'MySQLConfig', 'OracleConfig', 'SQLiteConfig', 'RedisConfig',
    # 连接类
    'MySQLConnection', 'OracleConnection', 'SQLiteConnection', 'RedisConnection',
    # 操作类
    'BaseOperations', 'QueryBuilder', 'TransactionManager',
    # 便捷函数
    'get_mysql', 'get_oracle', 'get_sqlite', 'get_redis',
    'df_to_sql', 'db_exist'
]


def db(target_names: Union[List[str], str, None],
       table_name: Union[str, None],
       condition: Union[Dict[str, Any], str, None],
       db_type: str,
       force_query: bool = False) -> Union[pd.DataFrame, str]:
    """
    兼容原有的db函数接口
    
    Args:
        target_names: 查询目标字段
        table_name: 表名
        condition: 查询条件
        db_type: 数据库类型
        force_query: 强制作为查询处理
        
    Returns:
        查询结果
    """
    try:
        # 创建数据库操作实例
        operations = DatabaseFactory.create_operations(db_type)
        
        # 执行查询
        return operations.query(target_names, table_name, condition, force_query)
        
    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        raise


def query(sql: str, 
          db_type: str, 
          params: Optional[Any] = None) -> pd.DataFrame:
    """
    执行查询SQL
    
    Args:
        sql: SQL语句
        db_type: 数据库类型
        params: 参数
        
    Returns:
        查询结果DataFrame
    """
    operations = DatabaseFactory.create_operations(db_type)
    return operations.execute_raw_sql(sql, params)


def execute(sql: str, 
           db_type: str, 
           params: Optional[Any] = None) -> int:
    """
    执行非查询SQL
    
    Args:
        sql: SQL语句
        db_type: 数据库类型
        params: 参数
        
    Returns:
        影响的行数
    """
    operations = DatabaseFactory.create_operations(db_type)
    return operations.execute_raw_sql(sql, params)


def transaction(db_type: str):
    """
    获取事务管理器
    
    Args:
        db_type: 数据库类型
        
    Returns:
        事务管理器
    """
    return DatabaseFactory.create_transaction_manager(db_type)


def get_mysql(config: Optional[Dict[str, Any]] = None, 
              instance_name: str = "default") -> MySQLConnection:
    """获取MySQL连接"""
    return DatabaseFactory.create_connection(DatabaseType.MYSQL, config, instance_name)


def get_oracle(config: Optional[Dict[str, Any]] = None,
               instance_name: str = "default") -> OracleConnection:
    """获取Oracle连接"""
    return DatabaseFactory.create_connection(DatabaseType.ORACLE, config, instance_name)


def get_sqlite(database_name: str = "default",
               instance_name: str = "default") -> SQLiteConnection:
    """获取SQLite连接"""
    config = {"database_name": database_name}
    return DatabaseFactory.create_connection(DatabaseType.SQLITE, config, instance_name)


def get_redis(config: Optional[Dict[str, Any]] = None,
              instance_name: str = "default") -> RedisConnection:
    """获取Redis连接"""
    return DatabaseFactory.create_connection(DatabaseType.REDIS, config, instance_name)


def df_to_sql(data: pd.DataFrame, table_name: str, db_type: str = "mysql"):
    """
    将DataFrame写入数据库
    
    Args:
        data: DataFrame数据
        table_name: 表名
        db_type: 数据库类型
    """
    if db_type.lower() == "mysql":
        mysql_conn = get_mysql()
        engine = mysql_conn.get_sqlalchemy_engine()
        data.to_sql(table_name, con=engine, if_exists='append', chunksize=10000, index=False)
    else:
        raise NotImplementedError(f"暂不支持 {db_type} 的DataFrame写入")


def db_exist(key: str, value: List[str], table: str, db_type: str) -> bool:
    """
    检查数据是否存在（兼容原有函数）
    
    Args:
        key: 字段名
        value: 值范围 [start, end]
        table: 表名
        db_type: 数据库类型
        
    Returns:
        是否存在
    """
    sql = f"SELECT EXISTS (SELECT 1 FROM {table} WHERE {key} BETWEEN '{value[0]}' AND '{value[1]}') AS exists_flag"
    result = query(sql, db_type)
    return result.iloc[0, 0] == 1 if not result.empty else False


# 创建查询构建器的便捷函数
def select(*fields) -> QueryBuilder:
    """创建SELECT查询构建器"""
    builder = QueryBuilder()
    if fields:
        builder.select(*fields)
    return builder


def from_table(table_name: str) -> QueryBuilder:
    """创建FROM查询构建器"""
    return QueryBuilder(table_name)


# 示例使用方法
def _example_usage():
    """使用示例"""
    
    # 1. 兼容原有的db函数调用
    phone = db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': '25078'}, 'oracle')
    
    # 2. 新的查询方式
    result = query("SELECT * FROM users WHERE id = %s", "mysql", (1,))
    
    # 3. 使用查询构建器
    sql = (select("name", "email")
           .from_table("users")
           .where({"status": "active"})
           .order_by("name")
           .limit(10)
           .build())
    
    # 4. 使用事务
    with transaction("mysql").transaction() as cursor:
        cursor.execute("INSERT INTO users (name) VALUES (%s)", ("test",))
        cursor.execute("UPDATE users SET status = %s WHERE id = %s", ("active", 1))
    
    # 5. 直接获取连接
    mysql_conn = get_mysql()
    with mysql_conn.get_cursor() as cursor:
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()


if __name__ == "__main__":
    # 运行示例
    _example_usage()
