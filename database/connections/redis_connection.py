"""
Redis数据库连接类
"""
from typing import Any, Optional
import redis
import logging

from .base_connection import BaseConnection
from ..config.redis_config import RedisConfig

logger = logging.getLogger(__name__)


class RedisConnection(BaseConnection):
    """Redis数据库连接"""
    
    def __init__(self, config: RedisConfig):
        super().__init__(config)
        self.create_pool()
        self._client = None
    
    def create_pool(self):
        """创建Redis连接池"""
        try:
            pool_params = self.config.get_pool_params()
            self._pool = redis.ConnectionPool(**pool_params)
            logger.info("Redis连接池创建成功")
        except Exception as e:
            logger.error(f"Redis连接池创建失败: {e}")
            raise
    
    def get_connection(self):
        """获取Redis连接"""
        if not self._pool:
            self.create_pool()
        return redis.Redis(connection_pool=self._pool)
    
    def close_connection(self, connection):
        """关闭Redis连接"""
        # Redis连接通过连接池管理，不需要手动关闭
        pass
    
    def _create_cursor(self, connection):
        """Redis不需要游标，直接返回连接"""
        return connection
    
    def execute_query(self, sql: str, params: Optional[Any] = None):
        """Redis不支持SQL查询"""
        raise NotImplementedError("Redis不支持SQL查询操作")
    
    def execute_non_query(self, sql: str, params: Optional[Any] = None) -> int:
        """Redis不支持SQL操作"""
        raise NotImplementedError("Redis不支持SQL操作")
    
    def get_client(self):
        """获取Redis客户端"""
        if not self._client:
            self._client = self.get_connection()
        return self._client
    
    def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """设置键值对"""
        client = self.get_client()
        return client.set(key, value, ex=ex)
    
    def get(self, key: str) -> Optional[str]:
        """获取键对应的值"""
        client = self.get_client()
        return client.get(key)
    
    def delete(self, *keys) -> int:
        """删除键"""
        client = self.get_client()
        return client.delete(*keys)
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        client = self.get_client()
        return client.exists(key) > 0
    
    def expire(self, key: str, time: int) -> bool:
        """设置键的过期时间"""
        client = self.get_client()
        return client.expire(key, time)
    
    def ttl(self, key: str) -> int:
        """获取键的剩余生存时间"""
        client = self.get_client()
        return client.ttl(key)
    
    @classmethod
    def from_url(cls, url: str, db: int = 0) -> 'RedisConnection':
        """从URL创建Redis连接"""
        config = RedisConfig.from_url(url, db)
        return cls(config)
    
    @classmethod
    def from_mysql_decrypt(cls, mysql_connection, db: int = 2) -> 'RedisConnection':
        """从MySQL解密配置创建Redis连接（兼容原有方式）"""
        redis_url = mysql_connection.fetch_decrypt(
            "SELECT value FROM info_db WHERE index_information = 'redis'",
            None,
            'value'
        )
        return cls.from_url(redis_url, db)
