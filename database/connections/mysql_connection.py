"""
MySQL数据库连接类
"""
from typing import Any, Optional, Tuple, List
import pymysql
from dbutils.pooled_db import PooledDB
from sqlalchemy import create_engine
import logging

from .base_connection import BaseConnection
from ..config.mysql_config import MySQLConfig

logger = logging.getLogger(__name__)


class MySQLConnection(BaseConnection):
    """MySQL数据库连接"""
    
    def __init__(self, config: MySQLConfig):
        super().__init__(config)
        self.create_pool()
        self._engine = None
    
    def create_pool(self):
        """创建MySQL连接池"""
        try:
            pool_params = self.config.get_pool_params()
            connection_params = self.config.get_connection_params()
            
            self._pool = PooledDB(
                creator=pymysql,
                **pool_params,
                **connection_params
            )
            logger.info("MySQL连接池创建成功")
        except Exception as e:
            logger.error(f"MySQL连接池创建失败: {e}")
            raise
    
    def get_connection(self):
        """从连接池获取MySQL连接"""
        if not self._pool:
            self.create_pool()
        return self._pool.connection()
    
    def close_connection(self, connection):
        """关闭MySQL连接"""
        if connection:
            connection.close()
    
    def _create_cursor(self, connection):
        """创建MySQL游标"""
        connection.autocommit = False
        return connection.cursor(pymysql.cursors.DictCursor)
    
    def execute_query(self, sql: str, params: Optional[Any] = None) -> Tuple[List[str], List[List[Any]]]:
        """执行MySQL查询语句"""
        with self.get_cursor(commit=False) as cursor:
            cursor.execute(sql, params)
            result = cursor.fetchall()
            
            if result:
                headers = list(result[0].keys())
                content = [list(row.values()) for row in result]
                return headers, content
            else:
                return [], []
    
    def execute_non_query(self, sql: str, params: Optional[Any] = None) -> int:
        """执行MySQL非查询语句"""
        with self.get_cursor(commit=True) as cursor:
            cursor.execute(sql, params)
            return cursor.rowcount
    
    def fetch_decrypt(self, sql: str, params: Optional[Any], count_key: str) -> str:
        """执行解密查询（兼容原有方法）"""
        from function.methods_general import decrypt
        from register import K
        
        with self.get_cursor(commit=False) as cursor:
            # 获取解密密钥
            cursor.execute("SELECT value FROM info_db WHERE index_information = 'key'", None)
            data = cursor.fetchone()
            key_m = data['value']
            key = decrypt(K, key_m)
            
            # 执行查询并解密
            cursor.execute(sql, params)
            data = cursor.fetchone()
            data_m = data[count_key]
            return decrypt(key, data_m)
    
    def get_sqlalchemy_engine(self):
        """获取SQLAlchemy引擎"""
        if not self._engine:
            self._engine = create_engine(self.config.get_sqlalchemy_url())
        return self._engine
    
    @classmethod
    def from_register(cls) -> 'MySQLConnection':
        """从register模块创建MySQL连接"""
        config = MySQLConfig.from_register()
        return cls(config)
