"""
数据库连接基类
提供所有数据库连接的通用接口和基础实现
"""
from abc import ABC, abstractmethod
from typing import Any, Optional, Tuple, List, Dict
from contextlib import contextmanager
import logging
from timeit import default_timer

from ..exceptions import ConnectionError, QueryError, handle_database_error, db_logger

logger = logging.getLogger(__name__)


class BaseConnection(ABC):
    """数据库连接基类"""
    
    def __init__(self, config):
        self.config = config
        self._connection = None
        self._cursor = None
        self._pool = None
        
    @abstractmethod
    def create_pool(self):
        """创建连接池"""
        pass
    
    @abstractmethod
    def get_connection(self):
        """从连接池获取连接"""
        pass
    
    @abstractmethod
    def close_connection(self, connection):
        """关闭连接"""
        pass
    
    @abstractmethod
    def execute_query(self, sql: str, params: Optional[Any] = None) -> Tuple[List[str], List[List[Any]]]:
        """执行查询语句"""
        pass
    
    @abstractmethod
    def execute_non_query(self, sql: str, params: Optional[Any] = None) -> int:
        """执行非查询语句（INSERT, UPDATE, DELETE）"""
        pass
    
    @contextmanager
    @handle_database_error
    def get_cursor(self, commit: bool = True, log_time: bool = False, log_label: str = ''):
        """获取游标的上下文管理器"""
        start_time = default_timer() if log_time else None
        connection = None
        cursor = None

        try:
            connection = self.get_connection()
            if not connection:
                raise ConnectionError("无法获取数据库连接")

            cursor = self._create_cursor(connection)
            db_cursor = DatabaseCursor(cursor, connection, self)

            db_logger.log_connection(self.__class__.__name__, "default", True)

            yield db_cursor

            if commit:
                connection.commit()
                db_logger.log_transaction("提交", True)

        except Exception as e:
            if connection:
                try:
                    connection.rollback()
                    db_logger.log_transaction("回滚", True)
                except Exception as rollback_error:
                    db_logger.log_error("回滚失败", rollback_error)

            db_logger.log_error("数据库操作", e)
            raise QueryError(f"数据库操作失败: {e}", e)
        finally:
            if cursor:
                try:
                    cursor.close()
                except Exception as e:
                    logger.warning(f"关闭游标失败: {e}")

            if connection:
                try:
                    self.close_connection(connection)
                except Exception as e:
                    logger.warning(f"关闭连接失败: {e}")

            if log_time and start_time:
                elapsed = default_timer() - start_time
                label = log_label or f"{self.__class__.__name__}操作"
                db_logger.logger.info(f"-- {label}: {elapsed:.6f} 秒")
    
    @abstractmethod
    def _create_cursor(self, connection):
        """创建游标"""
        pass
    
    def test_connection(self) -> bool:
        """测试连接是否正常"""
        try:
            with self.get_cursor(commit=False, log_time=True, log_label="连接测试") as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                db_logger.log_connection(self.__class__.__name__, "test", True)
                return True
        except Exception as e:
            db_logger.log_connection(self.__class__.__name__, "test", False)
            db_logger.log_error("连接测试", e)
            return False


class DatabaseCursor:
    """数据库游标包装类"""
    
    def __init__(self, cursor, connection, db_connection):
        self.cursor = cursor
        self.connection = connection
        self.db_connection = db_connection
    
    def execute(self, sql: str, params: Optional[Any] = None):
        """执行SQL语句"""
        return self.cursor.execute(sql, params)
    
    def fetchall(self):
        """获取所有结果"""
        return self.cursor.fetchall()
    
    def fetchone(self):
        """获取一条结果"""
        return self.cursor.fetchone()
    
    def fetchmany(self, size: int):
        """获取指定数量的结果"""
        return self.cursor.fetchmany(size)
    
    @property
    def rowcount(self):
        """获取影响的行数"""
        return self.cursor.rowcount
    
    @property
    def description(self):
        """获取列描述"""
        return self.cursor.description
