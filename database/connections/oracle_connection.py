"""
Oracle数据库连接类
"""
from typing import Any, Optional, Tuple, List
import oracledb
from dbutils.pooled_db import PooledDB
import logging

from .base_connection import BaseConnection
from ..config.oracle_config import OracleConfig

logger = logging.getLogger(__name__)


class OracleConnection(BaseConnection):
    """Oracle数据库连接"""
    
    def __init__(self, config: OracleConfig):
        super().__init__(config)
        self.create_pool()
    
    def create_pool(self):
        """创建Oracle连接池"""
        try:
            pool_params = self.config.get_pool_params()
            connection_params = self.config.get_connection_params()
            
            self._pool = PooledDB(
                creator=oracledb,
                **pool_params,
                **connection_params
            )
            logger.info("Oracle连接池创建成功")
        except oracledb.Error as e:
            logger.error(f"Oracle连接池创建失败: {e}")
            raise
    
    def get_connection(self):
        """从连接池获取Oracle连接"""
        if not self._pool:
            self.create_pool()
        return self._pool.connection()
    
    def close_connection(self, connection):
        """关闭Oracle连接"""
        if connection:
            connection.close()
    
    def _create_cursor(self, connection):
        """创建Oracle游标"""
        connection.autocommit = False
        return connection.cursor()
    
    def execute_query(self, sql: str, params: Optional[Any] = None) -> Tuple[List[str], List[List[Any]]]:
        """执行Oracle查询语句"""
        with self.get_cursor(commit=False) as cursor:
            cursor.execute(sql, params)
            
            # 获取列名
            headers = [desc[0] for desc in cursor.description] if cursor.description else []
            
            # 获取数据并处理LOB对象
            content = cursor.fetchall()
            if content:
                content_processed = [[self._convert_to_str(col) for col in row] for row in content]
                return headers, content_processed
            else:
                return headers, []
    
    def execute_non_query(self, sql: str, params: Optional[Any] = None) -> int:
        """执行Oracle非查询语句"""
        with self.get_cursor(commit=True) as cursor:
            cursor.execute(sql, params)
            return cursor.rowcount
    
    @staticmethod
    def _convert_to_str(value):
        """将LOB对象转换为字符串"""
        if isinstance(value, oracledb.LOB):
            return value.read()
        else:
            return value
    
    @classmethod
    def from_encrypted_config(cls, encrypted_config: str) -> 'OracleConnection':
        """从加密配置创建Oracle连接"""
        config = OracleConfig.from_encrypted_config(encrypted_config)
        return cls(config)
    
    @classmethod
    def from_mysql_decrypt(cls, mysql_connection) -> 'OracleConnection':
        """从MySQL解密配置创建Oracle连接（兼容原有方式）"""
        his_str = mysql_connection.fetch_decrypt(
            "SELECT value FROM info_db WHERE index_information = 'his'", 
            None, 
            'value'
        )
        return cls.from_encrypted_config(his_str)
