"""
SQLite数据库连接类
"""
from typing import Any, Optional, Tuple, List
import sqlite3
import regex as re
import logging

from .base_connection import BaseConnection
from ..config.sqlite_config import SQLiteConfig

logger = logging.getLogger(__name__)


def regexp(pattern, item):
    """正则表达式函数，用于SQLite的REGEXP操作"""
    return re.search(pattern, item) is not None


class SQLiteConnection(BaseConnection):
    """SQLite数据库连接"""
    
    def __init__(self, config: SQLiteConfig):
        super().__init__(config)
        # SQLite通常不需要连接池，但为了接口一致性保留
        self.create_pool()
    
    def create_pool(self):
        """创建SQLite连接池（实际上SQLite不需要连接池）"""
        # SQLite是文件数据库，不需要真正的连接池
        # 这里只是为了接口一致性
        logger.info("SQLite连接配置完成")
    
    def get_connection(self):
        """获取SQLite连接"""
        connection_params = self.config.get_connection_params()
        connection = sqlite3.connect(**connection_params)
        
        # 启用正则表达式支持
        connection.create_function('REGEXP', 2, regexp)
        
        return connection
    
    def close_connection(self, connection):
        """关闭SQLite连接"""
        if connection:
            connection.close()
    
    def _create_cursor(self, connection):
        """创建SQLite游标"""
        return connection.cursor()
    
    def execute_query(self, sql: str, params: Optional[Any] = None) -> Tuple[List[str], List[List[Any]]]:
        """执行SQLite查询语句"""
        with self.get_cursor(commit=False) as cursor:
            cursor.execute(sql, params)
            
            # 获取列名
            headers = [desc[0] for desc in cursor.description] if cursor.description else []
            
            # 获取数据
            content = cursor.fetchall()
            
            return headers, content
    
    def execute_non_query(self, sql: str, params: Optional[Any] = None) -> int:
        """执行SQLite非查询语句"""
        with self.get_cursor(commit=True) as cursor:
            cursor.execute(sql, params)
            return cursor.rowcount
    
    @classmethod
    def from_name(cls, database_name: str, database_path: str = 'app/resource/db') -> 'SQLiteConnection':
        """从数据库名称创建SQLite连接"""
        config = SQLiteConfig.from_name(database_name, database_path)
        return cls(config)
