# 数据库模块迁移指南

## 概述

本指南帮助您从原有的`db`模块迁移到重构后的`database`模块。重构后的模块完全向后兼容，您可以逐步迁移而无需一次性修改所有代码。

## 迁移策略

### 阶段1: 无缝替换（立即可用）

重构后的模块完全兼容原有接口，您只需要修改导入语句：

**原有代码:**
```python
from db.execute import db, df_to_sql, db_exist
```

**新代码:**
```python
from database import db, df_to_sql, db_exist
```

所有原有的函数调用都无需修改：
```python
# 这些调用完全不变
phone = db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': '25078'}, 'oracle')
users = db(['name', 'email'], 'users', {'status': 'active'}, 'mysql')
df_to_sql(dataframe, 'table_name')
exists = db_exist('created_at', ['2024-01-01', '2024-12-31'], 'users', 'mysql')
```

### 阶段2: 采用新接口（推荐）

在新功能开发中，建议使用新的接口：

#### 2.1 查询操作

**原有方式:**
```python
result = db(None, None, "SELECT * FROM users WHERE id = 1", 'mysql')
```

**新方式:**
```python
from database import query
result = query("SELECT * FROM users WHERE id = %s", "mysql", (1,))
```

#### 2.2 执行操作

**原有方式:**
```python
affected = db(None, None, "UPDATE users SET status = 'active' WHERE id = 1", 'mysql')
```

**新方式:**
```python
from database import execute
affected = execute("UPDATE users SET status = %s WHERE id = %s", "mysql", ('active', 1))
```

#### 2.3 查询构建器

**原有方式:**
```python
# 手动拼接SQL
sql = f"SELECT name, email FROM users WHERE status = 'active' ORDER BY name LIMIT 10"
result = db(None, None, sql, 'mysql')
```

**新方式:**
```python
from database import select
sql = (select("name", "email")
       .from_table("users")
       .where({"status": "active"})
       .order_by("name")
       .limit(10)
       .build())
result = query(sql, "mysql")
```

### 阶段3: 高级功能（可选）

#### 3.1 事务管理

**原有方式:**
```python
from db.mysql import UsingMysql

with UsingMysql() as dbm:
    dbm.cursor.execute("INSERT INTO users (name) VALUES ('test')")
    dbm.cursor.execute("UPDATE users SET status = 'active' WHERE id = 1")
```

**新方式:**
```python
from database import transaction

with transaction("mysql").transaction() as cursor:
    cursor.execute("INSERT INTO users (name) VALUES (%s)", ("test",))
    cursor.execute("UPDATE users SET status = %s WHERE id = %s", ("active", 1))
```

#### 3.2 直接连接管理

**原有方式:**
```python
from db.mysql import UsingMysql

with UsingMysql(log_time=True) as dbm:
    dbm.cursor.execute("SELECT COUNT(*) FROM users")
    count = dbm.cursor.fetchone()
```

**新方式:**
```python
from database import get_mysql

mysql_conn = get_mysql()
with mysql_conn.get_cursor(log_time=True, log_label="用户统计") as cursor:
    cursor.execute("SELECT COUNT(*) FROM users")
    count = cursor.fetchone()
```

## 具体迁移步骤

### 步骤1: 更新导入语句

在每个使用数据库的文件中，更新导入语句：

```python
# 原有导入
from db.execute import db, df_to_sql, db_exist
from db.mysql import UsingMysql, engine
from db.oracle import UsingOracle
from db.sqlite import UsingSQLite
from db.redis import redis_client

# 新的导入
from database import db, df_to_sql, db_exist, query, execute, transaction
from database import get_mysql, get_oracle, get_sqlite, get_redis
```

### 步骤2: 测试兼容性

运行现有的测试用例，确保所有功能正常工作：

```python
# 测试原有功能
def test_compatibility():
    # 测试基本查询
    result = db('name', 'users', {'id': 1}, 'mysql')
    assert result is not None
    
    # 测试DataFrame操作
    import pandas as pd
    df = pd.DataFrame({'name': ['test'], 'email': ['<EMAIL>']})
    df_to_sql(df, 'test_table')
    
    # 测试存在性检查
    exists = db_exist('created_at', ['2024-01-01', '2024-12-31'], 'users', 'mysql')
    assert isinstance(exists, bool)
```

### 步骤3: 逐步采用新接口

在新功能中使用新接口，在维护旧功能时可以选择性地重构：

```python
# 新功能使用新接口
def get_user_statistics():
    from database import query, select
    
    # 使用查询构建器
    sql = (select("status", "COUNT(*) as count")
           .from_table("users")
           .group_by("status")
           .build())
    
    return query(sql, "mysql")

# 旧功能保持不变或选择性重构
def get_user_phone(empl_id):
    # 保持原有调用方式
    return db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': empl_id}, 'oracle')
```

## 配置迁移

### 原有配置方式

原有模块的配置分散在各个文件中：

```python
# db/mysql.py 中的硬编码配置
dip_params = {
    'host': '***************',
    'port': 33060,
    'user': decrypt(K, USE),
    'password': decrypt(K, PW),
    'db': 'yzzxyy',
}
```

### 新的配置方式

新模块支持更灵活的配置管理：

```python
from database.config import MySQLConfig
from database import DatabaseFactory, DatabaseType

# 方式1: 使用默认配置（自动从register模块加载）
mysql_conn = DatabaseFactory.create_connection(DatabaseType.MYSQL)

# 方式2: 使用自定义配置
custom_config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'username',
    'password': 'password',
    'database': 'dbname'
}
mysql_conn = DatabaseFactory.create_connection(DatabaseType.MYSQL, custom_config)

# 方式3: 使用配置类
config = MySQLConfig(
    host='localhost',
    port=3306,
    user='username',
    password='password',
    database='dbname'
)
```

## 性能对比

| 功能 | 原有模块 | 重构后模块 | 改进 |
|------|----------|------------|------|
| 连接管理 | 每次创建新连接 | 连接池复用 | 性能提升 |
| 错误处理 | 基础异常处理 | 统一异常体系 | 更好的调试 |
| 日志记录 | 简单打印 | 结构化日志 | 更好的监控 |
| 代码维护 | 功能分散 | 分层架构 | 更易维护 |
| 接口一致性 | 不同接口风格 | 统一接口设计 | 更易使用 |

## 常见问题

### Q1: 迁移后性能会有影响吗？

A: 不会。重构后的模块使用连接池管理，实际上性能会有所提升。

### Q2: 原有的代码需要全部重写吗？

A: 不需要。重构后的模块完全向后兼容，原有代码无需修改。

### Q3: 如何处理配置信息？

A: 原有的配置会自动加载。如果需要自定义配置，可以使用新的配置类。

### Q4: 事务处理有什么变化？

A: 新模块提供了更简洁的事务管理接口，但原有的方式仍然可用。

### Q5: 如何调试数据库问题？

A: 新模块提供了详细的日志记录，可以通过日志快速定位问题。

## 迁移检查清单

- [ ] 更新所有导入语句
- [ ] 运行现有测试用例
- [ ] 验证数据库连接正常
- [ ] 检查日志输出
- [ ] 测试事务操作
- [ ] 验证DataFrame操作
- [ ] 检查错误处理
- [ ] 性能测试对比
- [ ] 更新文档和注释

## 技术支持

如果在迁移过程中遇到问题，可以：

1. 查看详细的错误日志
2. 参考`database/examples.py`中的示例
3. 运行`database/tests/test_database.py`进行测试
4. 查看`database/README.md`了解详细用法

迁移是一个渐进的过程，建议先在测试环境中验证，然后逐步在生产环境中应用。
