"""
数据库模块使用示例
展示重构后数据库模块的各种使用方式
"""
import pandas as pd
from typing import List, Dict, Any

# 导入重构后的数据库模块
from database import (
    # 兼容接口
    db, query, execute, transaction,
    # 便捷函数
    get_mysql, get_oracle, get_sqlite, get_redis,
    # 查询构建器
    select, from_table,
    # 工具函数
    df_to_sql, db_exist
)
from database.operations import QueryBuilder, InsertBuilder, UpdateBuilder, DeleteBuilder
from database.exceptions import DatabaseError, setup_logging


def example_1_compatibility():
    """示例1: 兼容原有接口"""
    print("=== 示例1: 兼容原有接口 ===")
    
    try:
        # 完全兼容原有的db函数调用
        phone = db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': '25078'}, 'oracle')
        print(f"查询电话号码: {phone}")
        
        # 查询多个字段
        users = db(['name', 'email'], 'users', {'status': 'active'}, 'mysql')
        print(f"活跃用户: {users}")
        
        # 执行原始SQL
        count = db(None, None, "SELECT COUNT(*) as total FROM users", 'mysql')
        print(f"用户总数: {count}")
        
    except Exception as e:
        print(f"兼容接口示例失败: {e}")


def example_2_new_interfaces():
    """示例2: 新的便捷接口"""
    print("\n=== 示例2: 新的便捷接口 ===")
    
    try:
        # 直接执行查询
        result = query("SELECT id, name, email FROM users LIMIT 5", "mysql")
        print(f"查询结果:\n{result}")
        
        # 执行非查询SQL
        affected_rows = execute(
            "UPDATE users SET last_login = NOW() WHERE id = %s", 
            "mysql", 
            (1,)
        )
        print(f"更新影响行数: {affected_rows}")
        
        # 检查数据是否存在
        exists = db_exist('created_at', ['2024-01-01', '2024-12-31'], 'users', 'mysql')
        print(f"2024年是否有用户数据: {exists}")
        
    except Exception as e:
        print(f"新接口示例失败: {e}")


def example_3_query_builder():
    """示例3: 查询构建器"""
    print("\n=== 示例3: 查询构建器 ===")
    
    try:
        # 使用链式调用构建复杂查询
        sql = (select("u.name", "u.email", "p.title")
               .from_table("users u")
               .inner_join("posts p", "u.id = p.user_id")
               .where({"u.status": "active"})
               .where_between("u.created_at", "2024-01-01", "2024-12-31")
               .order_by("u.name", "ASC")
               .limit(10)
               .build())
        
        print(f"构建的SQL:\n{sql}")
        
        # 执行构建的查询
        result = query(sql, "mysql")
        print(f"查询结果:\n{result}")
        
        # INSERT语句构建
        insert_sql = (InsertBuilder("users")
                     .values(name="新用户", email="<EMAIL>", status="active")
                     .build())
        print(f"INSERT SQL: {insert_sql}")
        
        # UPDATE语句构建
        update_sql = (UpdateBuilder("users")
                     .set(status="inactive", updated_at="NOW()")
                     .where({"last_login": None})
                     .build())
        print(f"UPDATE SQL: {update_sql}")
        
    except Exception as e:
        print(f"查询构建器示例失败: {e}")


def example_4_transactions():
    """示例4: 事务操作"""
    print("\n=== 示例4: 事务操作 ===")
    
    try:
        # 使用事务管理器
        tx_manager = transaction("mysql")
        
        # 单个事务中执行多个操作
        with tx_manager.transaction() as cursor:
            cursor.execute("INSERT INTO users (name, email) VALUES (%s, %s)", 
                          ("事务用户1", "<EMAIL>"))
            cursor.execute("INSERT INTO users (name, email) VALUES (%s, %s)", 
                          ("事务用户2", "<EMAIL>"))
            print("事务操作完成")
        
        # 批量插入
        users_data = [
            {"name": "批量用户1", "email": "<EMAIL>", "status": "active"},
            {"name": "批量用户2", "email": "<EMAIL>", "status": "active"},
            {"name": "批量用户3", "email": "<EMAIL>", "status": "active"}
        ]
        
        inserted_count = tx_manager.batch_insert("users", users_data)
        print(f"批量插入了 {inserted_count} 条记录")
        
        # 批量更新
        update_data = [
            {"id": 1, "status": "premium"},
            {"id": 2, "status": "premium"}
        ]
        updated_count = tx_manager.batch_update("users", update_data, ["id"])
        print(f"批量更新了 {updated_count} 条记录")
        
    except Exception as e:
        print(f"事务操作示例失败: {e}")


def example_5_direct_connections():
    """示例5: 直接使用连接"""
    print("\n=== 示例5: 直接使用连接 ===")
    
    try:
        # 获取MySQL连接
        mysql_conn = get_mysql()
        
        # 使用连接执行查询
        with mysql_conn.get_cursor(log_time=True, log_label="用户查询") as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM users")
            result = cursor.fetchone()
            print(f"用户总数: {result}")
        
        # 测试连接
        is_connected = mysql_conn.test_connection()
        print(f"MySQL连接状态: {'正常' if is_connected else '异常'}")
        
        # 获取SQLAlchemy引擎（用于pandas）
        engine = mysql_conn.get_sqlalchemy_engine()
        print(f"SQLAlchemy引擎: {engine}")
        
        # 使用Redis连接
        try:
            redis_conn = get_redis()
            redis_conn.set("test_key", "test_value", ex=60)
            value = redis_conn.get("test_key")
            print(f"Redis测试: {value}")
        except Exception as e:
            print(f"Redis连接失败: {e}")
        
    except Exception as e:
        print(f"直接连接示例失败: {e}")


def example_6_dataframe_operations():
    """示例6: DataFrame操作"""
    print("\n=== 示例6: DataFrame操作 ===")
    
    try:
        # 创建测试数据
        df = pd.DataFrame({
            'name': ['DF用户1', 'DF用户2', 'DF用户3'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'status': ['active', 'active', 'inactive']
        })
        
        print(f"要写入的DataFrame:\n{df}")
        
        # 将DataFrame写入数据库
        df_to_sql(df, "users_temp", "mysql")
        print("DataFrame写入成功")
        
        # 从数据库读取数据到DataFrame
        result_df = query("SELECT * FROM users_temp", "mysql")
        print(f"从数据库读取的数据:\n{result_df}")
        
    except Exception as e:
        print(f"DataFrame操作示例失败: {e}")


def example_7_error_handling():
    """示例7: 错误处理"""
    print("\n=== 示例7: 错误处理 ===")
    
    try:
        # 故意执行错误的SQL来演示错误处理
        result = query("SELECT * FROM non_existent_table", "mysql")
        
    except DatabaseError as e:
        print(f"捕获到数据库错误: {e}")
        print(f"原始错误: {e.original_error}")
    
    except Exception as e:
        print(f"捕获到其他错误: {e}")


def example_8_advanced_features():
    """示例8: 高级功能"""
    print("\n=== 示例8: 高级功能 ===")
    
    try:
        # 使用工厂模式创建多个连接实例
        from database import DatabaseFactory, DatabaseType
        
        # 创建自定义配置的MySQL连接
        custom_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'custom_user',
            'password': 'custom_pass',
            'database': 'custom_db'
        }
        
        # 注意：这里只是演示，实际使用时需要确保配置正确
        print("演示工厂模式创建连接（需要正确的配置）")
        
        # 列出所有活跃连接
        connections = DatabaseFactory.list_connections()
        print(f"活跃连接: {connections}")
        
        # 使用查询构建器的高级功能
        complex_query = (QueryBuilder("orders o")
                        .select("o.id", "o.total", "u.name", "COUNT(oi.id) as item_count")
                        .inner_join("users u", "o.user_id = u.id")
                        .left_join("order_items oi", "o.id = oi.order_id")
                        .where({"o.status": "completed"})
                        .where_between("o.created_at", "2024-01-01", "2024-12-31")
                        .group_by("o.id", "o.total", "u.name")
                        .having("COUNT(oi.id) > 0")
                        .order_by("o.total", "DESC")
                        .limit(20)
                        .build())
        
        print(f"复杂查询SQL:\n{complex_query}")
        
    except Exception as e:
        print(f"高级功能示例失败: {e}")


def run_all_examples():
    """运行所有示例"""
    print("开始运行数据库模块使用示例...")
    
    # 设置日志
    setup_logging()
    
    # 运行各个示例
    example_1_compatibility()
    example_2_new_interfaces()
    example_3_query_builder()
    example_4_transactions()
    example_5_direct_connections()
    example_6_dataframe_operations()
    example_7_error_handling()
    example_8_advanced_features()
    
    print("\n所有示例运行完成！")


if __name__ == "__main__":
    run_all_examples()
