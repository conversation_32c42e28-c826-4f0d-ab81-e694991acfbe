"""
事务管理器
提供数据库事务的统一管理
"""
from contextlib import contextmanager
from typing import Any, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class TransactionManager:
    """事务管理器"""
    
    def __init__(self, connection):
        self.connection = connection
    
    @contextmanager
    def transaction(self, rollback_on_exception: bool = True):
        """
        事务上下文管理器
        
        Args:
            rollback_on_exception: 异常时是否回滚
        """
        with self.connection.get_cursor(commit=False) as cursor:
            try:
                yield cursor
                cursor.connection.commit()
                logger.debug("事务提交成功")
            except Exception as e:
                if rollback_on_exception:
                    cursor.connection.rollback()
                    logger.error(f"事务回滚: {e}")
                else:
                    logger.error(f"事务异常但未回滚: {e}")
                raise
    
    def execute_in_transaction(self, 
                             operations: list, 
                             rollback_on_exception: bool = True) -> list:
        """
        在事务中执行多个操作
        
        Args:
            operations: 操作列表，每个操作是(sql, params)元组
            rollback_on_exception: 异常时是否回滚
            
        Returns:
            每个操作的结果列表
        """
        results = []
        
        with self.transaction(rollback_on_exception) as cursor:
            for sql, params in operations:
                cursor.execute(sql, params)
                
                if sql.strip().upper().startswith('SELECT'):
                    # 查询操作，获取结果
                    result = cursor.fetchall()
                    results.append(result)
                else:
                    # 非查询操作，获取影响行数
                    results.append(cursor.rowcount)
        
        return results
    
    def batch_insert(self, 
                    table_name: str, 
                    data_list: list, 
                    batch_size: int = 1000) -> int:
        """
        批量插入数据
        
        Args:
            table_name: 表名
            data_list: 数据列表，每个元素是字典
            batch_size: 批次大小
            
        Returns:
            总插入行数
        """
        if not data_list:
            return 0
        
        total_inserted = 0
        
        # 获取列名
        columns = list(data_list[0].keys())
        columns_str = ", ".join(columns)
        placeholders = ", ".join(["%s"] * len(columns))
        
        sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        with self.transaction() as cursor:
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                values = [tuple(row[col] for col in columns) for row in batch]
                
                cursor.executemany(sql, values)
                total_inserted += cursor.rowcount
                
                logger.debug(f"批量插入 {len(batch)} 条记录到 {table_name}")
        
        logger.info(f"总共插入 {total_inserted} 条记录到 {table_name}")
        return total_inserted
    
    def batch_update(self, 
                    table_name: str, 
                    data_list: list, 
                    key_columns: list,
                    batch_size: int = 1000) -> int:
        """
        批量更新数据
        
        Args:
            table_name: 表名
            data_list: 数据列表，每个元素是字典
            key_columns: 用于WHERE条件的键列
            batch_size: 批次大小
            
        Returns:
            总更新行数
        """
        if not data_list:
            return 0
        
        total_updated = 0
        
        # 构建UPDATE SQL
        all_columns = list(data_list[0].keys())
        update_columns = [col for col in all_columns if col not in key_columns]
        
        set_clause = ", ".join([f"{col} = %s" for col in update_columns])
        where_clause = " AND ".join([f"{col} = %s" for col in key_columns])
        
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        
        with self.transaction() as cursor:
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                values = []
                
                for row in batch:
                    # 更新值 + WHERE条件值
                    update_values = [row[col] for col in update_columns]
                    key_values = [row[col] for col in key_columns]
                    values.append(tuple(update_values + key_values))
                
                cursor.executemany(sql, values)
                total_updated += cursor.rowcount
                
                logger.debug(f"批量更新 {len(batch)} 条记录在 {table_name}")
        
        logger.info(f"总共更新 {total_updated} 条记录在 {table_name}")
        return total_updated
    
    def execute_with_retry(self, 
                          operation: Callable,
                          max_retries: int = 3,
                          retry_exceptions: tuple = (Exception,)) -> Any:
        """
        带重试的操作执行
        
        Args:
            operation: 要执行的操作函数
            max_retries: 最大重试次数
            retry_exceptions: 需要重试的异常类型
            
        Returns:
            操作结果
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return operation()
            except retry_exceptions as e:
                last_exception = e
                if attempt < max_retries:
                    logger.warning(f"操作失败，第 {attempt + 1} 次重试: {e}")
                else:
                    logger.error(f"操作失败，已达到最大重试次数 {max_retries}: {e}")
        
        raise last_exception
