"""
SQL查询构建器
提供链式调用的SQL构建功能
"""
from typing import List, Dict, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)


class QueryBuilder:
    """SQL查询构建器"""
    
    def __init__(self, table_name: str = None):
        self._table = table_name
        self._select_fields = []
        self._where_conditions = []
        self._join_clauses = []
        self._group_by_fields = []
        self._having_conditions = []
        self._order_by_fields = []
        self._limit_count = None
        self._offset_count = None
        self._distinct = False
        
    def select(self, *fields: str) -> 'QueryBuilder':
        """选择字段"""
        self._select_fields.extend(fields)
        return self
    
    def from_table(self, table_name: str) -> 'QueryBuilder':
        """设置表名"""
        self._table = table_name
        return self
    
    def where(self, condition: Union[str, Dict[str, Any]]) -> 'QueryBuilder':
        """添加WHERE条件"""
        if isinstance(condition, dict):
            for key, value in condition.items():
                if isinstance(value, str):
                    self._where_conditions.append(f"{key} = '{value}'")
                elif isinstance(value, (list, tuple)):
                    # IN条件
                    values = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in value])
                    self._where_conditions.append(f"{key} IN ({values})")
                else:
                    self._where_conditions.append(f"{key} = {value}")
        else:
            self._where_conditions.append(condition)
        return self
    
    def where_between(self, field: str, start_value: Any, end_value: Any) -> 'QueryBuilder':
        """添加BETWEEN条件"""
        if isinstance(start_value, str):
            condition = f"{field} BETWEEN '{start_value}' AND '{end_value}'"
        else:
            condition = f"{field} BETWEEN {start_value} AND {end_value}"
        self._where_conditions.append(condition)
        return self
    
    def where_like(self, field: str, pattern: str) -> 'QueryBuilder':
        """添加LIKE条件"""
        self._where_conditions.append(f"{field} LIKE '{pattern}'")
        return self
    
    def where_in(self, field: str, values: List[Any]) -> 'QueryBuilder':
        """添加IN条件"""
        value_str = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in values])
        self._where_conditions.append(f"{field} IN ({value_str})")
        return self
    
    def join(self, table: str, on_condition: str, join_type: str = "INNER") -> 'QueryBuilder':
        """添加JOIN"""
        self._join_clauses.append(f"{join_type} JOIN {table} ON {on_condition}")
        return self
    
    def left_join(self, table: str, on_condition: str) -> 'QueryBuilder':
        """添加LEFT JOIN"""
        return self.join(table, on_condition, "LEFT")
    
    def right_join(self, table: str, on_condition: str) -> 'QueryBuilder':
        """添加RIGHT JOIN"""
        return self.join(table, on_condition, "RIGHT")
    
    def inner_join(self, table: str, on_condition: str) -> 'QueryBuilder':
        """添加INNER JOIN"""
        return self.join(table, on_condition, "INNER")
    
    def group_by(self, *fields: str) -> 'QueryBuilder':
        """添加GROUP BY"""
        self._group_by_fields.extend(fields)
        return self
    
    def having(self, condition: str) -> 'QueryBuilder':
        """添加HAVING条件"""
        self._having_conditions.append(condition)
        return self
    
    def order_by(self, field: str, direction: str = "ASC") -> 'QueryBuilder':
        """添加ORDER BY"""
        self._order_by_fields.append(f"{field} {direction}")
        return self
    
    def limit(self, count: int) -> 'QueryBuilder':
        """添加LIMIT"""
        self._limit_count = count
        return self
    
    def offset(self, count: int) -> 'QueryBuilder':
        """添加OFFSET"""
        self._offset_count = count
        return self
    
    def distinct(self) -> 'QueryBuilder':
        """添加DISTINCT"""
        self._distinct = True
        return self
    
    def build(self) -> str:
        """构建SQL语句"""
        if not self._table:
            raise ValueError("表名不能为空")
        
        # SELECT子句
        if self._select_fields:
            select_clause = ", ".join(self._select_fields)
        else:
            select_clause = "*"
        
        if self._distinct:
            select_clause = f"DISTINCT {select_clause}"
        
        sql = f"SELECT {select_clause}"
        
        # FROM子句
        sql += f" FROM {self._table}"
        
        # JOIN子句
        if self._join_clauses:
            sql += " " + " ".join(self._join_clauses)
        
        # WHERE子句
        if self._where_conditions:
            sql += " WHERE " + " AND ".join(self._where_conditions)
        
        # GROUP BY子句
        if self._group_by_fields:
            sql += " GROUP BY " + ", ".join(self._group_by_fields)
        
        # HAVING子句
        if self._having_conditions:
            sql += " HAVING " + " AND ".join(self._having_conditions)
        
        # ORDER BY子句
        if self._order_by_fields:
            sql += " ORDER BY " + ", ".join(self._order_by_fields)
        
        # LIMIT子句
        if self._limit_count is not None:
            sql += f" LIMIT {self._limit_count}"
        
        # OFFSET子句
        if self._offset_count is not None:
            sql += f" OFFSET {self._offset_count}"
        
        return sql
    
    def __str__(self) -> str:
        """返回构建的SQL语句"""
        return self.build()


class InsertBuilder:
    """INSERT语句构建器"""
    
    def __init__(self, table_name: str):
        self._table = table_name
        self._data = {}
        self._on_duplicate_key_update = {}
    
    def values(self, **kwargs) -> 'InsertBuilder':
        """设置插入值"""
        self._data.update(kwargs)
        return self
    
    def on_duplicate_key_update(self, **kwargs) -> 'InsertBuilder':
        """设置重复键更新"""
        self._on_duplicate_key_update.update(kwargs)
        return self
    
    def build(self) -> str:
        """构建INSERT SQL"""
        if not self._data:
            raise ValueError("插入数据不能为空")
        
        columns = ", ".join(self._data.keys())
        values = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in self._data.values()])
        
        sql = f"INSERT INTO {self._table} ({columns}) VALUES ({values})"
        
        if self._on_duplicate_key_update:
            update_clause = ", ".join([
                f"{k} = '{v}'" if isinstance(v, str) else f"{k} = {v}"
                for k, v in self._on_duplicate_key_update.items()
            ])
            sql += f" ON DUPLICATE KEY UPDATE {update_clause}"
        
        return sql


class UpdateBuilder:
    """UPDATE语句构建器"""
    
    def __init__(self, table_name: str):
        self._table = table_name
        self._set_data = {}
        self._where_conditions = []
    
    def set(self, **kwargs) -> 'UpdateBuilder':
        """设置更新值"""
        self._set_data.update(kwargs)
        return self
    
    def where(self, condition: Union[str, Dict[str, Any]]) -> 'UpdateBuilder':
        """添加WHERE条件"""
        if isinstance(condition, dict):
            for key, value in condition.items():
                if isinstance(value, str):
                    self._where_conditions.append(f"{key} = '{value}'")
                else:
                    self._where_conditions.append(f"{key} = {value}")
        else:
            self._where_conditions.append(condition)
        return self
    
    def build(self) -> str:
        """构建UPDATE SQL"""
        if not self._set_data:
            raise ValueError("更新数据不能为空")
        
        set_clause = ", ".join([
            f"{k} = '{v}'" if isinstance(v, str) else f"{k} = {v}"
            for k, v in self._set_data.items()
        ])
        
        sql = f"UPDATE {self._table} SET {set_clause}"
        
        if self._where_conditions:
            sql += " WHERE " + " AND ".join(self._where_conditions)
        
        return sql


class DeleteBuilder:
    """DELETE语句构建器"""
    
    def __init__(self, table_name: str):
        self._table = table_name
        self._where_conditions = []
    
    def where(self, condition: Union[str, Dict[str, Any]]) -> 'DeleteBuilder':
        """添加WHERE条件"""
        if isinstance(condition, dict):
            for key, value in condition.items():
                if isinstance(value, str):
                    self._where_conditions.append(f"{key} = '{value}'")
                else:
                    self._where_conditions.append(f"{key} = {value}")
        else:
            self._where_conditions.append(condition)
        return self
    
    def build(self) -> str:
        """构建DELETE SQL"""
        sql = f"DELETE FROM {self._table}"
        
        if self._where_conditions:
            sql += " WHERE " + " AND ".join(self._where_conditions)
        
        return sql
