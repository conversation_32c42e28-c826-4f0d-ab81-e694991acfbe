"""
数据库操作基类
提供统一的数据库操作接口
"""
from abc import ABC, abstractmethod
from typing import Union, List, Dict, Any, Optional
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class BaseOperations(ABC):
    """数据库操作基类"""
    
    def __init__(self, connection):
        self.connection = connection
    
    def query(self, 
              target_names: Union[List[str], str, None],
              table_name: Optional[str] = None,
              condition: Union[Dict[str, Any], str, None] = None,
              force_query: bool = False) -> Union[pd.DataFrame, str]:
        """
        统一的查询接口
        
        Args:
            target_names: 查询目标字段，可以是列表、字符串或None
            table_name: 表名
            condition: 查询条件，可以是字典或SQL字符串
            force_query: 强制作为查询处理
            
        Returns:
            DataFrame或字符串结果
        """
        try:
            sql = self._build_sql(target_names, table_name, condition)
            
            if self._is_query_sql(sql) or force_query:
                return self._execute_query(sql, target_names)
            else:
                return self._execute_non_query(sql)
                
        except Exception as e:
            logger.error(f"查询执行失败: {e}, SQL: {sql}")
            raise
    
    def _build_sql(self, target_names, table_name, condition) -> str:
        """构建SQL语句"""
        if table_name is None and condition is not None:
            # 直接使用condition作为完整SQL
            return condition
        
        # 构建SELECT语句
        if isinstance(target_names, list):
            target_str = ", ".join(target_names)
        elif isinstance(target_names, str):
            target_str = target_names
        else:
            target_str = "*"
        
        sql = f"SELECT {target_str} FROM {table_name}"
        
        if condition:
            if isinstance(condition, dict):
                where_clause = " AND ".join([f"{key} = '{value}'" for key, value in condition.items()])
                sql += f" WHERE {where_clause}"
            elif isinstance(condition, str):
                sql += f" WHERE {condition}"
        
        return sql
    
    def _is_query_sql(self, sql: str) -> bool:
        """判断是否为查询SQL"""
        return sql.strip().upper().startswith('SELECT')
    
    def _execute_query(self, sql: str, target_names) -> Union[pd.DataFrame, str]:
        """执行查询SQL"""
        headers, content = self.connection.execute_query(sql)
        df = pd.DataFrame(content, columns=headers)
        
        if isinstance(target_names, str) and not df.empty:
            # 返回单个字段的第一个值
            return df[target_names].iloc[0] if target_names in df.columns else ''
        
        return df
    
    def _execute_non_query(self, sql: str) -> str:
        """执行非查询SQL"""
        affected_rows = self.connection.execute_non_query(sql)
        return str(affected_rows)
    
    def execute_raw_sql(self, sql: str, params: Optional[Any] = None) -> Union[pd.DataFrame, int]:
        """执行原始SQL"""
        try:
            if self._is_query_sql(sql):
                headers, content = self.connection.execute_query(sql, params)
                return pd.DataFrame(content, columns=headers)
            else:
                return self.connection.execute_non_query(sql, params)
        except Exception as e:
            logger.error(f"原始SQL执行失败: {e}, SQL: {sql}")
            raise
    
    def insert(self, table_name: str, data: Dict[str, Any]) -> int:
        """插入数据"""
        columns = ", ".join(data.keys())
        values = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in data.values()])
        sql = f"INSERT INTO {table_name} ({columns}) VALUES ({values})"
        return self.connection.execute_non_query(sql)
    
    def update(self, table_name: str, data: Dict[str, Any], condition: Dict[str, Any]) -> int:
        """更新数据"""
        set_clause = ", ".join([f"{k} = '{v}'" if isinstance(v, str) else f"{k} = {v}" 
                               for k, v in data.items()])
        where_clause = " AND ".join([f"{k} = '{v}'" if isinstance(v, str) else f"{k} = {v}" 
                                    for k, v in condition.items()])
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        return self.connection.execute_non_query(sql)
    
    def delete(self, table_name: str, condition: Dict[str, Any]) -> int:
        """删除数据"""
        where_clause = " AND ".join([f"{k} = '{v}'" if isinstance(v, str) else f"{k} = {v}" 
                                    for k, v in condition.items()])
        sql = f"DELETE FROM {table_name} WHERE {where_clause}"
        return self.connection.execute_non_query(sql)
    
    def exists(self, table_name: str, condition: Dict[str, Any]) -> bool:
        """检查数据是否存在"""
        where_clause = " AND ".join([f"{k} = '{v}'" if isinstance(v, str) else f"{k} = {v}" 
                                    for k, v in condition.items()])
        sql = f"SELECT EXISTS(SELECT 1 FROM {table_name} WHERE {where_clause}) AS exists_flag"
        result = self.execute_raw_sql(sql)
        return result.iloc[0, 0] == 1 if not result.empty else False
