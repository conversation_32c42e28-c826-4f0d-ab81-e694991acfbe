"""
SQLite数据库配置类
"""
from typing import Dict, Any
from .base_config import BaseConfig


class SQLiteConfig(BaseConfig):
    """SQLite数据库配置"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.database_name = kwargs.get('database_name')
        self.database_path = kwargs.get('database_path', 'app/resource/db')
        self.check_same_thread = kwargs.get('check_same_thread', False)
        self.timeout = kwargs.get('timeout', 20.0)
        self.isolation_level = kwargs.get('isolation_level', None)
        
    def get_connection_params(self) -> Dict[str, Any]:
        """获取SQLite连接参数"""
        db_file = f"{self.database_path}/{self.database_name}.db"
        return {
            'database': db_file,
            'check_same_thread': self.check_same_thread,
            'timeout': self.timeout,
            'isolation_level': self.isolation_level
        }
    
    def get_pool_params(self) -> Dict[str, Any]:
        """获取SQLite连接池参数（SQLite通常不需要连接池）"""
        return {
            'maxconnections': 1,  # SQLite通常单连接
            'mincached': 0,
            'maxcached': 1,
            'maxshared': 0,
            'blocking': True,
            'maxusage': None,
            'setsession': None,
            'reset': False
        }
    
    def get_required_fields(self) -> list:
        """获取必需的配置字段"""
        return ['database_name']
    
    def get_database_file_path(self) -> str:
        """获取数据库文件完整路径"""
        return f"{self.database_path}/{self.database_name}.db"
    
    @classmethod
    def from_name(cls, database_name: str, database_path: str = 'app/resource/db') -> 'SQLiteConfig':
        """从数据库名称创建配置"""
        return cls(
            database_name=database_name,
            database_path=database_path
        )
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'SQLiteConfig':
        """从字典创建配置"""
        return cls(**config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'database_name': self.database_name,
            'database_path': self.database_path,
            'check_same_thread': self.check_same_thread,
            'isolation_level': self.isolation_level
        })
        return base_dict
