"""
Oracle数据库配置类
"""
from typing import Dict, Any
from .base_config import BaseConfig


class OracleConfig(BaseConfig):
    """Oracle数据库配置"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.dsn = kwargs.get('dsn')
        self.encoding = kwargs.get('encoding', 'utf-8')
        self.nencoding = kwargs.get('nencoding', 'utf-8')
        self.threaded = kwargs.get('threaded', True)
        
    def get_connection_params(self) -> Dict[str, Any]:
        """获取Oracle连接参数"""
        return {
            'dsn': self.dsn,
            'user': self.user,
            'password': self.password,
            'encoding': self.encoding,
            'nencoding': self.nencoding,
            'threaded': self.threaded
        }
    
    def get_pool_params(self) -> Dict[str, Any]:
        """获取Oracle连接池参数"""
        return {
            'maxconnections': self.max_connections,
            'mincached': self.min_cached,
            'maxcached': self.max_cached,
            'maxshared': self.max_shared,
            'blocking': self.blocking,
            'maxusage': self.max_usage,
            'setsession': None,
            'reset': self.reset
        }
    
    def get_required_fields(self) -> list:
        """获取必需的配置字段"""
        return ['dsn', 'user', 'password']
    
    @classmethod
    def from_encrypted_config(cls, encrypted_config: str) -> 'OracleConfig':
        """从加密配置字符串创建配置"""
        import ast
        config_dict = ast.literal_eval(encrypted_config)
        return cls(**config_dict)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'OracleConfig':
        """从字典创建配置"""
        return cls(**config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'dsn': self.dsn,
            'encoding': self.encoding,
            'nencoding': self.nencoding,
            'threaded': self.threaded
        })
        return base_dict
