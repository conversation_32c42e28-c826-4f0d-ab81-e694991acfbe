"""
MySQL数据库配置类
"""
from typing import Dict, Any
from .base_config import BaseConfig
from function.methods_general import decrypt


class MySQLConfig(BaseConfig):
    """MySQL数据库配置"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.charset = kwargs.get('charset', 'utf8mb4')
        self.autocommit = kwargs.get('autocommit', False)
        self.connect_timeout = kwargs.get('connect_timeout', 10)
        self.read_timeout = kwargs.get('read_timeout', 30)
        self.write_timeout = kwargs.get('write_timeout', 30)
        
    def get_connection_params(self) -> Dict[str, Any]:
        """获取MySQL连接参数"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.user,
            'password': self.password,
            'charset': self.charset,
            'autocommit': self.autocommit,
            'connect_timeout': self.connect_timeout,
            'read_timeout': self.read_timeout,
            'write_timeout': self.write_timeout
        }
    
    def get_pool_params(self) -> Dict[str, Any]:
        """获取MySQL连接池参数"""
        return {
            'maxconnections': self.max_connections,
            'mincached': self.min_cached,
            'maxcached': self.max_cached,
            'maxshared': self.max_shared,
            'blocking': self.blocking,
            'maxusage': self.max_usage,
            'setsession': None,
            'reset': self.reset
        }
    
    def get_required_fields(self) -> list:
        """获取必需的配置字段"""
        return ['host', 'port', 'user', 'password', 'database']
    
    def get_sqlalchemy_url(self) -> str:
        """获取SQLAlchemy连接URL"""
        from urllib.parse import quote
        return (f'mysql+pymysql://{self.user}:{quote(self.password)}'
                f'@{self.host}:{self.port}/{self.database}?charset={self.charset}')
    
    @classmethod
    def from_register(cls) -> 'MySQLConfig':
        """从register模块加载配置"""
        try:
            from register import K, USE, PW
            return cls(
                host='***************',
                port=33060,
                user=decrypt(K, USE),
                password=decrypt(K, PW),
                database='chronic_disease_Platform'
            )
        except ImportError as e:
            raise ImportError(f"无法导入register模块: {e}")
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'MySQLConfig':
        """从字典创建配置"""
        return cls(**config_dict)
