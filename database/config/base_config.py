"""
数据库配置基类
提供所有数据库配置的通用接口和基础实现
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseConfig(ABC):
    """数据库配置基类"""
    
    def __init__(self, **kwargs):
        self.host: Optional[str] = kwargs.get('host')
        self.port: Optional[int] = kwargs.get('port')
        self.user: Optional[str] = kwargs.get('user')
        self.password: Optional[str] = kwargs.get('password')
        self.database: Optional[str] = kwargs.get('database')
        
        # 连接池配置
        self.min_cached: int = kwargs.get('min_cached', 1)
        self.max_cached: int = kwargs.get('max_cached', 20)
        self.max_shared: int = kwargs.get('max_shared', 10)
        self.max_connections: int = kwargs.get('max_connections', 50)
        self.blocking: bool = kwargs.get('blocking', True)
        self.max_usage: int = kwargs.get('max_usage', 100)
        self.reset: bool = kwargs.get('reset', True)
        
        # 其他配置
        self.charset: str = kwargs.get('charset', 'utf8mb4')
        self.timeout: int = kwargs.get('timeout', 30)
        
    @abstractmethod
    def get_connection_params(self) -> Dict[str, Any]:
        """获取连接参数"""
        pass
    
    @abstractmethod
    def get_pool_params(self) -> Dict[str, Any]:
        """获取连接池参数"""
        pass
    
    def validate(self) -> bool:
        """验证配置是否有效"""
        required_fields = self.get_required_fields()
        for field in required_fields:
            if not hasattr(self, field) or getattr(self, field) is None:
                logger.error(f"配置验证失败: 缺少必需字段 {field}")
                return False
        return True
    
    @abstractmethod
    def get_required_fields(self) -> list:
        """获取必需的配置字段"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'host': self.host,
            'port': self.port,
            'user': self.user,
            'password': self.password,
            'database': self.database,
            'min_cached': self.min_cached,
            'max_cached': self.max_cached,
            'max_shared': self.max_shared,
            'max_connections': self.max_connections,
            'blocking': self.blocking,
            'max_usage': self.max_usage,
            'reset': self.reset,
            'charset': self.charset,
            'timeout': self.timeout
        }
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(host={self.host}, port={self.port}, database={self.database})"
