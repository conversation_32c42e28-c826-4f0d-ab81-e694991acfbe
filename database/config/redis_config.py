"""
Redis数据库配置类
"""
from typing import Dict, Any
from .base_config import BaseConfig


class RedisConfig(BaseConfig):
    """Redis数据库配置"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db = kwargs.get('db', 0)
        self.decode_responses = kwargs.get('decode_responses', True)
        self.socket_connect_timeout = kwargs.get('socket_connect_timeout', 3)
        self.socket_timeout = kwargs.get('socket_timeout', 5)
        self.socket_keepalive = kwargs.get('socket_keepalive', True)
        self.retry_on_timeout = kwargs.get('retry_on_timeout', True)
        self.max_connections = kwargs.get('max_connections', 50)
        self.url = kwargs.get('url')  # 完整的Redis URL
        
    def get_connection_params(self) -> Dict[str, Any]:
        """获取Redis连接参数"""
        if self.url:
            return {
                'url': f"{self.url}/{self.db}",
                'decode_responses': self.decode_responses,
                'socket_connect_timeout': self.socket_connect_timeout,
                'socket_timeout': self.socket_timeout,
                'socket_keepalive': self.socket_keepalive,
                'retry_on_timeout': self.retry_on_timeout
            }
        else:
            return {
                'host': self.host,
                'port': self.port,
                'db': self.db,
                'password': self.password,
                'decode_responses': self.decode_responses,
                'socket_connect_timeout': self.socket_connect_timeout,
                'socket_timeout': self.socket_timeout,
                'socket_keepalive': self.socket_keepalive,
                'retry_on_timeout': self.retry_on_timeout
            }
    
    def get_pool_params(self) -> Dict[str, Any]:
        """获取Redis连接池参数"""
        params = self.get_connection_params()
        params['max_connections'] = self.max_connections
        return params
    
    def get_required_fields(self) -> list:
        """获取必需的配置字段"""
        if self.url:
            return ['url']
        else:
            return ['host', 'port']
    
    @classmethod
    def from_url(cls, url: str, db: int = 0) -> 'RedisConfig':
        """从URL创建配置"""
        return cls(url=url, db=db)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'RedisConfig':
        """从字典创建配置"""
        return cls(**config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'db': self.db,
            'decode_responses': self.decode_responses,
            'socket_connect_timeout': self.socket_connect_timeout,
            'socket_timeout': self.socket_timeout,
            'socket_keepalive': self.socket_keepalive,
            'retry_on_timeout': self.retry_on_timeout,
            'url': self.url
        })
        return base_dict
