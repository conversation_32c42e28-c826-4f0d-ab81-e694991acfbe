"""
数据库模块测试用例
测试重构后的数据库模块的各项功能
"""
import unittest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from database import (
    db, query, execute, transaction,
    DatabaseFactory, DatabaseType,
    MySQLConfig, OracleConfig, SQLiteConfig, RedisConfig,
    select, from_table
)
from database.exceptions import DatabaseError, ConnectionError, QueryError


class TestDatabaseConfig(unittest.TestCase):
    """测试数据库配置类"""
    
    def test_mysql_config(self):
        """测试MySQL配置"""
        config = MySQLConfig(
            host='localhost',
            port=3306,
            user='test',
            password='test',
            database='test_db'
        )
        
        self.assertTrue(config.validate())
        self.assertEqual(config.host, 'localhost')
        self.assertEqual(config.port, 3306)
        
        # 测试连接参数
        params = config.get_connection_params()
        self.assertIn('host', params)
        self.assertIn('port', params)
        self.assertIn('database', params)
    
    def test_oracle_config(self):
        """测试Oracle配置"""
        config = OracleConfig(
            dsn='localhost:1521/xe',
            user='test',
            password='test'
        )
        
        self.assertTrue(config.validate())
        self.assertEqual(config.dsn, 'localhost:1521/xe')
    
    def test_sqlite_config(self):
        """测试SQLite配置"""
        config = SQLiteConfig(database_name='test')
        
        self.assertTrue(config.validate())
        self.assertEqual(config.database_name, 'test')
    
    def test_redis_config(self):
        """测试Redis配置"""
        config = RedisConfig(url='redis://localhost:6379')
        
        self.assertTrue(config.validate())
        self.assertEqual(config.url, 'redis://localhost:6379')


class TestQueryBuilder(unittest.TestCase):
    """测试查询构建器"""
    
    def test_simple_select(self):
        """测试简单查询"""
        sql = (select("name", "email")
               .from_table("users")
               .build())
        
        expected = "SELECT name, email FROM users"
        self.assertEqual(sql, expected)
    
    def test_select_with_where(self):
        """测试带WHERE条件的查询"""
        sql = (select("*")
               .from_table("users")
               .where({"status": "active", "age": 25})
               .build())
        
        self.assertIn("SELECT * FROM users", sql)
        self.assertIn("WHERE", sql)
        self.assertIn("status = 'active'", sql)
        self.assertIn("age = 25", sql)
    
    def test_select_with_join(self):
        """测试带JOIN的查询"""
        sql = (select("u.name", "p.title")
               .from_table("users u")
               .inner_join("posts p", "u.id = p.user_id")
               .build())
        
        self.assertIn("INNER JOIN posts p ON u.id = p.user_id", sql)
    
    def test_select_with_order_limit(self):
        """测试带排序和限制的查询"""
        sql = (select("name")
               .from_table("users")
               .order_by("name", "ASC")
               .limit(10)
               .build())
        
        self.assertIn("ORDER BY name ASC", sql)
        self.assertIn("LIMIT 10", sql)


class TestDatabaseFactory(unittest.TestCase):
    """测试数据库工厂"""
    
    @patch('database.connections.MySQLConnection')
    def test_create_mysql_connection(self, mock_mysql):
        """测试创建MySQL连接"""
        mock_instance = Mock()
        mock_mysql.return_value = mock_instance
        
        with patch('database.config.MySQLConfig.from_register') as mock_config:
            mock_config.return_value = MySQLConfig(
                host='localhost', port=3306, user='test', 
                password='test', database='test'
            )
            
            conn = DatabaseFactory.create_connection(DatabaseType.MYSQL)
            self.assertIsNotNone(conn)
    
    def test_database_type_enum(self):
        """测试数据库类型枚举"""
        self.assertEqual(DatabaseType.MYSQL.value, "mysql")
        self.assertEqual(DatabaseType.ORACLE.value, "oracle")
        self.assertEqual(DatabaseType.SQLITE.value, "sqlite")
        self.assertEqual(DatabaseType.REDIS.value, "redis")


class TestDatabaseOperations(unittest.TestCase):
    """测试数据库操作"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_connection = Mock()
        self.mock_connection.execute_query.return_value = (
            ['id', 'name', 'email'],
            [[1, 'test', '<EMAIL>']]
        )
        self.mock_connection.execute_non_query.return_value = 1
    
    @patch('database.DatabaseFactory.create_operations')
    def test_db_function_compatibility(self, mock_create_ops):
        """测试db函数的兼容性"""
        mock_ops = Mock()
        mock_ops.query.return_value = pd.DataFrame({
            'name': ['test'],
            'email': ['<EMAIL>']
        })
        mock_create_ops.return_value = mock_ops
        
        # 测试原有的db函数调用方式
        result = db(['name', 'email'], 'users', {'id': 1}, 'mysql')
        
        self.assertIsInstance(result, pd.DataFrame)
        mock_ops.query.assert_called_once()
    
    @patch('database.DatabaseFactory.create_operations')
    def test_query_function(self, mock_create_ops):
        """测试query函数"""
        mock_ops = Mock()
        mock_ops.execute_raw_sql.return_value = pd.DataFrame({
            'count': [10]
        })
        mock_create_ops.return_value = mock_ops
        
        result = query("SELECT COUNT(*) as count FROM users", "mysql")
        
        self.assertIsInstance(result, pd.DataFrame)
        mock_ops.execute_raw_sql.assert_called_once()
    
    @patch('database.DatabaseFactory.create_operations')
    def test_execute_function(self, mock_create_ops):
        """测试execute函数"""
        mock_ops = Mock()
        mock_ops.execute_raw_sql.return_value = 1
        mock_create_ops.return_value = mock_ops
        
        result = execute("INSERT INTO users (name) VALUES ('test')", "mysql")
        
        self.assertEqual(result, 1)
        mock_ops.execute_raw_sql.assert_called_once()


class TestErrorHandling(unittest.TestCase):
    """测试错误处理"""
    
    def test_database_error(self):
        """测试数据库异常"""
        original_error = Exception("原始错误")
        db_error = DatabaseError("数据库错误", original_error)
        
        self.assertEqual(db_error.message, "数据库错误")
        self.assertEqual(db_error.original_error, original_error)
        self.assertIn("原始错误", str(db_error))
    
    def test_connection_error(self):
        """测试连接异常"""
        conn_error = ConnectionError("连接失败")
        
        self.assertIsInstance(conn_error, DatabaseError)
        self.assertEqual(conn_error.message, "连接失败")
    
    def test_query_error(self):
        """测试查询异常"""
        query_error = QueryError("查询失败")
        
        self.assertIsInstance(query_error, DatabaseError)
        self.assertEqual(query_error.message, "查询失败")


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @patch('database.get_mysql')
    def test_df_to_sql_integration(self, mock_get_mysql):
        """测试DataFrame写入集成"""
        mock_conn = Mock()
        mock_engine = Mock()
        mock_conn.get_sqlalchemy_engine.return_value = mock_engine
        mock_get_mysql.return_value = mock_conn
        
        # 创建测试数据
        df = pd.DataFrame({
            'name': ['test1', 'test2'],
            'email': ['<EMAIL>', '<EMAIL>']
        })
        
        from database import df_to_sql
        
        # 模拟to_sql方法
        with patch.object(df, 'to_sql') as mock_to_sql:
            df_to_sql(df, 'users')
            mock_to_sql.assert_called_once()
    
    def test_db_exist_function(self):
        """测试db_exist函数"""
        with patch('database.query') as mock_query:
            mock_query.return_value = pd.DataFrame({'exists_flag': [1]})
            
            from database import db_exist
            result = db_exist('created_at', ['2024-01-01', '2024-12-31'], 'users', 'mysql')
            
            self.assertTrue(result)
            mock_query.assert_called_once()


if __name__ == '__main__':
    # 设置测试日志级别
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # 运行测试
    unittest.main(verbosity=2)
