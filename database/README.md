# 重构后的数据库模块

## 概述

这是一个重构后的数据库操作模块，提供了更清晰的架构、更好的错误处理和更便捷的使用方式。同时完全兼容原有的`db()`函数接口。

## 架构设计

```
database/
├── config/          # 配置层 - 管理各种数据库的配置
├── connections/     # 连接层 - 管理数据库连接和连接池
├── operations/      # 操作层 - 提供统一的数据库操作接口
├── factory.py       # 工厂层 - 统一的实例创建接口
├── exceptions.py    # 异常处理 - 统一的错误处理机制
├── execute.py       # 执行层 - 简化的执行接口
└── __init__.py      # 统一接口 - 对外暴露的API
```



## 快速开始

### 1. 兼容原有接口

```python
from database import db

# 完全兼容原有的调用方式
phone = db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': '25078'}, 'oracle')
users = db(['name', 'email'], 'users', {'status': 'active'}, 'mysql')
```

### 2. 新的便捷接口

```python
from database import query, execute, select, from_table

# 直接执行SQL查询
result = query("SELECT * FROM users WHERE id = %s", "mysql", (1,))

# 执行非查询SQL
affected_rows = execute("UPDATE users SET status = %s WHERE id = %s", "mysql", ('active', 1))

# 使用查询构建器
sql = (select("name", "email")
       .from_table("users")
       .where({"status": "active"})
       .order_by("name")
       .limit(10)
       .build())
```

### 3. 事务操作

```python
from database import transaction

# 使用事务管理器
with transaction("mysql").transaction() as cursor:
    cursor.execute("INSERT INTO users (name) VALUES (%s)", ("test",))
    cursor.execute("UPDATE users SET status = %s WHERE id = %s", ("active", 1))
```

### 4. 直接获取连接

```python
from database import get_mysql, get_oracle, get_sqlite, get_redis

# 获取MySQL连接
mysql_conn = get_mysql()
with mysql_conn.get_cursor() as cursor:
    cursor.execute("SELECT COUNT(*) FROM users")
    count = cursor.fetchone()

# 获取Oracle连接
oracle_conn = get_oracle()

# 获取SQLite连接
sqlite_conn = get_sqlite("DRG")

# 获取Redis连接
redis_conn = get_redis()
redis_conn.set("key", "value", ex=3600)
```

## 详细使用说明

### 配置管理

```python
from database.config import MySQLConfig, OracleConfig

# 创建MySQL配置
mysql_config = MySQLConfig(
    host='localhost',
    port=3306,
    user='username',
    password='password',
    database='dbname'
)

# 从字典创建配置
config_dict = {
    'host': 'localhost',
    'port': 3306,
    'user': 'username',
    'password': 'password',
    'database': 'dbname'
}
mysql_config = MySQLConfig.from_dict(config_dict)
```

### 连接管理

```python
from database import DatabaseFactory, DatabaseType

# 使用工厂创建连接
mysql_conn = DatabaseFactory.create_connection(
    DatabaseType.MYSQL, 
    config_dict, 
    instance_name="custom"
)

# 获取已创建的连接
conn = DatabaseFactory.get_connection(DatabaseType.MYSQL, "custom")

# 关闭连接
DatabaseFactory.close_connection(DatabaseType.MYSQL, "custom")
```

### 查询构建器

```python
from database import QueryBuilder, InsertBuilder, UpdateBuilder, DeleteBuilder

# SELECT查询
query_builder = QueryBuilder("users")
sql = (query_builder
       .select("id", "name", "email")
       .where({"status": "active"})
       .where_between("created_at", "2024-01-01", "2024-12-31")
       .order_by("name", "ASC")
       .limit(10)
       .build())

# INSERT语句
insert_builder = InsertBuilder("users")
sql = (insert_builder
       .values(name="test", email="<EMAIL>")
       .build())

# UPDATE语句
update_builder = UpdateBuilder("users")
sql = (update_builder
       .set(status="inactive")
       .where({"id": 1})
       .build())

# DELETE语句
delete_builder = DeleteBuilder("users")
sql = (delete_builder
       .where({"status": "inactive"})
       .build())
```

### 批量操作

```python
from database import transaction

# 批量插入
tx_manager = transaction("mysql")
users_data = [
    {"name": "user1", "email": "<EMAIL>"},
    {"name": "user2", "email": "<EMAIL>"}
]
inserted_count = tx_manager.batch_insert("users", users_data)

# 批量更新
update_data = [
    {"id": 1, "status": "active"},
    {"id": 2, "status": "inactive"}
]
updated_count = tx_manager.batch_update("users", update_data, ["id"])
```

### 错误处理

```python
from database.exceptions import DatabaseError, ConnectionError, QueryError

try:
    result = query("SELECT * FROM users", "mysql")
except ConnectionError as e:
    print(f"连接错误: {e}")
except QueryError as e:
    print(f"查询错误: {e}")
except DatabaseError as e:
    print(f"数据库错误: {e}")
```

### 日志配置

```python
from database.exceptions import setup_logging
import logging

# 设置日志级别和格式
setup_logging(
    level=logging.INFO,
    format_string='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 迁移指南

### 从原有db模块迁移

1. **无需修改现有代码** - 原有的`db()`函数调用完全兼容
2. **逐步采用新接口** - 可以在新功能中使用新的接口
3. **配置迁移** - 原有的配置会自动加载，也可以使用新的配置类

### 性能优化建议

1. **复用连接** - 使用工厂模式创建的连接会自动复用
2. **批量操作** - 对于大量数据操作，使用批量插入/更新
3. **事务管理** - 合理使用事务减少数据库交互
4. **连接池配置** - 根据实际需求调整连接池参数

## 测试

```bash
# 运行测试
python -m pytest database/tests/test_database.py -v

# 或者直接运行测试文件
python database/tests/test_database.py
```

## 注意事项

1. **向后兼容** - 完全兼容原有的`db()`函数接口
2. **配置安全** - 敏感信息建议使用环境变量或加密存储
3. **连接管理** - 连接会自动管理，无需手动关闭
4. **错误处理** - 建议使用try-catch处理数据库异常
5. **日志记录** - 默认会记录操作日志，可根据需要调整级别

## 示例项目

查看`database/execute.py`中的完整示例，了解如何在实际项目中使用重构后的数据库模块。
