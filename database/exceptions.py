"""
数据库异常处理模块
定义统一的异常类型和错误处理机制
"""
import logging
from typing import Optional, Any

logger = logging.getLogger(__name__)


class DatabaseError(Exception):
    """数据库基础异常类"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)
    
    def __str__(self):
        if self.original_error:
            return f"{self.message} (原始错误: {self.original_error})"
        return self.message


class ConnectionError(DatabaseError):
    """数据库连接异常"""
    pass


class ConfigurationError(DatabaseError):
    """数据库配置异常"""
    pass


class QueryError(DatabaseError):
    """查询执行异常"""
    pass


class TransactionError(DatabaseError):
    """事务处理异常"""
    pass


class ValidationError(DatabaseError):
    """数据验证异常"""
    pass


def handle_database_error(func):
    """数据库错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DatabaseError:
            # 重新抛出自定义异常
            raise
        except Exception as e:
            # 包装其他异常
            logger.error(f"数据库操作异常: {func.__name__} - {e}")
            raise DatabaseError(f"数据库操作失败: {func.__name__}", e)
    
    return wrapper


def log_database_operation(operation_name: str, 
                          sql: Optional[str] = None,
                          params: Optional[Any] = None,
                          result: Optional[Any] = None,
                          error: Optional[Exception] = None):
    """记录数据库操作日志"""
    if error:
        logger.error(f"数据库操作失败 - {operation_name}: {error}")
        if sql:
            logger.error(f"SQL: {sql}")
        if params:
            logger.error(f"参数: {params}")
    else:
        logger.info(f"数据库操作成功 - {operation_name}")
        if sql:
            logger.debug(f"SQL: {sql}")
        if params:
            logger.debug(f"参数: {params}")
        if result is not None:
            logger.debug(f"结果: {result}")


class DatabaseLogger:
    """数据库操作日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_connection(self, db_type: str, instance_name: str, success: bool = True):
        """记录连接日志"""
        if success:
            self.logger.info(f"数据库连接成功 - {db_type}:{instance_name}")
        else:
            self.logger.error(f"数据库连接失败 - {db_type}:{instance_name}")
    
    def log_query(self, sql: str, params: Optional[Any] = None, 
                  execution_time: Optional[float] = None, 
                  row_count: Optional[int] = None):
        """记录查询日志"""
        log_msg = f"执行查询 - SQL: {sql}"
        if params:
            log_msg += f", 参数: {params}"
        if execution_time:
            log_msg += f", 耗时: {execution_time:.3f}秒"
        if row_count is not None:
            log_msg += f", 结果行数: {row_count}"
        
        self.logger.info(log_msg)
    
    def log_transaction(self, operation: str, success: bool = True):
        """记录事务日志"""
        if success:
            self.logger.info(f"事务操作成功 - {operation}")
        else:
            self.logger.error(f"事务操作失败 - {operation}")
    
    def log_error(self, operation: str, error: Exception, sql: Optional[str] = None):
        """记录错误日志"""
        error_msg = f"数据库错误 - {operation}: {error}"
        if sql:
            error_msg += f", SQL: {sql}"
        self.logger.error(error_msg)


# 全局日志记录器实例
db_logger = DatabaseLogger("database")


def setup_logging(level: int = logging.INFO, 
                 format_string: Optional[str] = None):
    """设置数据库模块的日志配置"""
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=level,
        format=format_string,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('database.log', encoding='utf-8')
        ]
    )
    
    # 设置数据库相关日志级别
    logging.getLogger('database').setLevel(level)
    logging.getLogger('sqlalchemy').setLevel(logging.WARNING)  # 减少SQLAlchemy日志
    logging.getLogger('pymysql').setLevel(logging.WARNING)     # 减少PyMySQL日志
