"""
简单的数据库模块测试
不依赖外部模块，仅测试核心功能
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

def test_query_builder():
    """测试查询构建器"""
    print("=== 测试查询构建器 ===")
    
    try:
        from operations.query_builder import QueryBuilder
        
        # 测试简单查询
        builder = QueryBuilder("users")
        sql = builder.select("name", "email").where({"status": "active"}).build()
        print(f"简单查询: {sql}")
        
        # 测试复杂查询
        complex_sql = (QueryBuilder("users u")
                      .select("u.name", "p.title")
                      .inner_join("posts p", "u.id = p.user_id")
                      .where({"u.status": "active"})
                      .order_by("u.name")
                      .limit(10)
                      .build())
        print(f"复杂查询: {complex_sql}")
        
        print("✓ 查询构建器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 查询构建器测试失败: {e}")
        return False


def test_config():
    """测试配置类"""
    print("\n=== 测试配置类 ===")
    
    try:
        from config.mysql_config import MySQLConfig
        from config.sqlite_config import SQLiteConfig
        
        # 测试MySQL配置
        mysql_config = MySQLConfig(
            host='localhost',
            port=3306,
            user='test',
            password='test',
            database='test_db'
        )
        
        print(f"MySQL配置: {mysql_config}")
        print(f"配置验证: {mysql_config.validate()}")
        
        # 测试SQLite配置
        sqlite_config = SQLiteConfig(database_name='test')
        print(f"SQLite配置: {sqlite_config}")
        print(f"配置验证: {sqlite_config.validate()}")
        
        print("✓ 配置类测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置类测试失败: {e}")
        return False


def test_exceptions():
    """测试异常处理"""
    print("\n=== 测试异常处理 ===")
    
    try:
        from exceptions import DatabaseError, ConnectionError, QueryError
        
        # 测试基础异常
        db_error = DatabaseError("测试错误")
        print(f"数据库异常: {db_error}")
        
        # 测试带原始异常的错误
        original_error = Exception("原始错误")
        wrapped_error = DatabaseError("包装错误", original_error)
        print(f"包装异常: {wrapped_error}")
        
        # 测试特定异常类型
        conn_error = ConnectionError("连接失败")
        query_error = QueryError("查询失败")
        
        print(f"连接异常: {conn_error}")
        print(f"查询异常: {query_error}")
        
        print("✓ 异常处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 异常处理测试失败: {e}")
        return False


def test_factory_basic():
    """测试工厂基础功能"""
    print("\n=== 测试工厂基础功能 ===")
    
    try:
        from factory import DatabaseType
        
        # 测试数据库类型枚举
        print(f"MySQL类型: {DatabaseType.MYSQL.value}")
        print(f"Oracle类型: {DatabaseType.ORACLE.value}")
        print(f"SQLite类型: {DatabaseType.SQLITE.value}")
        print(f"Redis类型: {DatabaseType.REDIS.value}")
        
        print("✓ 工厂基础功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 工厂基础功能测试失败: {e}")
        return False


def run_simple_tests():
    """运行简单测试"""
    print("开始运行数据库模块简单测试...\n")
    
    tests = [
        test_query_builder,
        test_config,
        test_exceptions,
        test_factory_basic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据库模块重构成功！")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    return passed == total


if __name__ == "__main__":
    success = run_simple_tests()
    exit(0 if success else 1)
