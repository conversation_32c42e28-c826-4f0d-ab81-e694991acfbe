"""
重构后的数据库执行模块
使用新的架构提供简化的数据库操作接口
"""
from typing import Union, List, Dict, Any, Optional
import pandas as pd
from pandas import DataFrame
import logging

from . import db, query, execute, df_to_sql, db_exist, get_mysql

logger = logging.getLogger(__name__)


def new_db(target_names: Union[List[str], str, None],
           table_name: Union[str, None],
           condition: Union[Dict[str, Any], str, None],
           db_type: str,
           force_query: bool = False) -> Union[pd.DataFrame, str]:
    """
    新的db函数实现，使用重构后的架构
    完全兼容原有的db函数接口
    
    Args:
        target_names: 查询目标字段
        table_name: 表名  
        condition: 查询条件
        db_type: 数据库类型
        force_query: 强制作为查询处理
        
    Returns:
        查询结果
    """
    return db(target_names, table_name, condition, db_type, force_query)


def new_df_to_sql(data: DataFrame, table_name: str, db_type: str = "mysql"):
    """
    新的DataFrame写入函数
    
    Args:
        data: DataFrame数据
        table_name: 表名
        db_type: 数据库类型
    """
    df_to_sql(data, table_name, db_type)


def new_db_exist(key: str, value: List[str], table: str, db_type: str) -> bool:
    """
    新的数据存在检查函数
    
    Args:
        key: 字段名
        value: 值范围
        table: 表名
        db_type: 数据库类型
        
    Returns:
        是否存在
    """
    return db_exist(key, value, table, db_type)


# 新增的便捷函数
def quick_query(sql: str, db_type: str = "mysql", params: Optional[Any] = None) -> pd.DataFrame:
    """
    快速查询函数
    
    Args:
        sql: SQL语句
        db_type: 数据库类型
        params: 参数
        
    Returns:
        查询结果
    """
    return query(sql, db_type, params)


def quick_execute(sql: str, db_type: str = "mysql", params: Optional[Any] = None) -> int:
    """
    快速执行函数
    
    Args:
        sql: SQL语句
        db_type: 数据库类型
        params: 参数
        
    Returns:
        影响行数
    """
    return execute(sql, db_type, params)


def get_user_info(user_id: str, db_type: str = "mysql") -> Dict[str, Any]:
    """
    获取用户信息的便捷函数
    
    Args:
        user_id: 用户ID
        db_type: 数据库类型
        
    Returns:
        用户信息字典
    """
    result = new_db(['id', 'username', 'email'], 'users', {'id': user_id}, db_type)
    if isinstance(result, pd.DataFrame) and not result.empty:
        return result.iloc[0].to_dict()
    return {}


def batch_insert_users(users_data: List[Dict[str, Any]], db_type: str = "mysql") -> int:
    """
    批量插入用户数据
    
    Args:
        users_data: 用户数据列表
        db_type: 数据库类型
        
    Returns:
        插入的行数
    """
    from . import transaction
    
    tx_manager = transaction(db_type)
    return tx_manager.batch_insert('users', users_data)


def get_table_count(table_name: str, db_type: str = "mysql") -> int:
    """
    获取表的记录数
    
    Args:
        table_name: 表名
        db_type: 数据库类型
        
    Returns:
        记录数
    """
    result = quick_query(f"SELECT COUNT(*) as count FROM {table_name}", db_type)
    return result.iloc[0]['count'] if not result.empty else 0


def test_connection(db_type: str = "mysql") -> bool:
    """
    测试数据库连接
    
    Args:
        db_type: 数据库类型
        
    Returns:
        连接是否正常
    """
    try:
        if db_type.lower() == "mysql":
            conn = get_mysql()
            return conn.test_connection()
        else:
            # 对其他数据库类型进行简单查询测试
            quick_query("SELECT 1", db_type)
            return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


# 演示新旧接口的对比
def demo_comparison():
    """演示新旧接口的使用对比"""
    
    print("=== 新旧接口对比演示 ===")
    
    # 1. 原有的调用方式（完全兼容）
    print("\n1. 原有调用方式:")
    try:
        phone = new_db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': '25078'}, 'oracle')
        print(f"查询结果: {phone}")
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 2. 新的便捷调用方式
    print("\n2. 新的便捷调用方式:")
    try:
        # 使用查询构建器
        from . import select
        sql = (select("name", "email")
               .from_table("users")
               .where({"status": "active"})
               .limit(5)
               .build())
        print(f"构建的SQL: {sql}")
        
        # 快速查询
        result = quick_query("SELECT COUNT(*) as total FROM users", "mysql")
        print(f"用户总数: {result}")
        
    except Exception as e:
        print(f"新方式查询失败: {e}")
    
    # 3. 事务操作演示
    print("\n3. 事务操作演示:")
    try:
        from . import transaction
        
        with transaction("mysql").transaction() as cursor:
            cursor.execute("SELECT 'Transaction test' as message")
            result = cursor.fetchone()
            print(f"事务测试结果: {result}")
            
    except Exception as e:
        print(f"事务操作失败: {e}")


if __name__ == "__main__":
    # 运行演示
    demo_comparison()
    
    # 测试连接
    print(f"\n数据库连接测试: {test_connection()}")
