"""
日志配置模块
"""
import logging
import logging.handlers
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from settings.config import settings


def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    if settings.log.file_path:
        log_file = Path(settings.log.file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, settings.log.level.upper()),
        format=settings.log.format,
        handlers=[]
    )
    
    # 获取根日志器
    root_logger = logging.getLogger()
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.log.level.upper()))
    console_formatter = logging.Formatter(settings.log.format)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果配置了文件路径）
    if settings.log.file_path:
        file_handler = logging.handlers.RotatingFileHandler(
            settings.log.file_path,
            maxBytes=settings.log.max_file_size,
            backupCount=settings.log.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, settings.log.level.upper()))
        file_formatter = logging.Formatter(settings.log.format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('celery').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('redis').setLevel(logging.WARNING)
    
    return root_logger


def get_logger(name: str = None) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name or __name__)


# 初始化日志配置
setup_logging()
