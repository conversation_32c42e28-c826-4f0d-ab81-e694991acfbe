"""
用户业务逻辑处理
"""
import bcrypt
import logging
from typing import Dict, Any, List, Optional

from database import select
from db.execute import db

logger = logging.getLogger(__name__)


class UserBusiness:
    """用户业务逻辑类"""
    
    @staticmethod
    def login(username: str, password: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 登录结果
        """
        try:
            # 查询用户
            sql = f"SELECT id, username, password, email FROM users WHERE username = '{username}'"
            users_df = db(None, None, sql, 'mysql')

            if users_df.empty:
                return {'success': False, 'message': '用户不存在'}

            user = users_df.iloc[0].to_dict()
            
            # 验证密码
            if bcrypt.checkpw(password.encode('utf-8'), user['password'].encode('utf-8')):
                return {
                    'success': True,
                    'message': '登录成功',
                    'data': {
                        'user_id': user['id'],
                        'username': user['username'],
                        'email': user['email']
                    }
                }
            else:
                return {'success': False, 'message': '密码错误'}
                
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return {'success': False, 'message': f'登录失败: {str(e)}'}
    
    @staticmethod
    def register(username: str, password: str, email: Optional[str] = None) -> Dict[str, Any]:
        """
        用户注册
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            
        Returns:
            Dict: 注册结果
        """
        try:
            # 检查用户是否已存在
            sql = f"SELECT id FROM users WHERE username = '{username}'"
            existing_users_df = db(None, None, sql, 'mysql')

            if not existing_users_df.empty:
                return {'success': False, 'message': '用户名已存在'}
            
            # 加密密码
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # 插入新用户
            sql = f"""
            INSERT INTO users (username, password, email, created_at)
            VALUES ('{username}', '{hashed_password}', '{email}', NOW())
            """
            result = db(None, None, sql, 'mysql')

            # 老项目的db方法对于非查询语句返回字符串形式的影响行数
            if result and result != '0':
                return {'success': True, 'message': '注册成功'}
            else:
                return {'success': False, 'message': '注册失败'}
                
        except Exception as e:
            logger.error(f"注册失败: {e}")
            return {'success': False, 'message': f'注册失败: {str(e)}'}
    
    @staticmethod
    def get_user_roles(user_id: int) -> Dict[str, Any]:
        """
        获取用户角色
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 用户角色信息
        """
        try:
            sql = """
            SELECT r.id, r.name, r.description 
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = :user_id
            """
            roles = select(sql, {'user_id': user_id})
            
            return {
                'success': True,
                'message': '获取用户角色成功',
                'data': roles
            }
            
        except Exception as e:
            logger.error(f"获取用户角色失败: {e}")
            return {'success': False, 'message': f'获取用户角色失败: {str(e)}'}
    
    @staticmethod
    def get_user_permissions(user_id: int) -> Dict[str, Any]:
        """
        获取用户权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 用户权限信息
        """
        try:
            sql = """
            SELECT DISTINCT p.id, p.name, p.description 
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = :user_id
            """
            permissions = select(sql, {'user_id': user_id})
            
            return {
                'success': True,
                'message': '获取用户权限成功',
                'data': permissions
            }
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return {'success': False, 'message': f'获取用户权限失败: {str(e)}'}
    
    @staticmethod
    def change_user_roles(user_id: int, role_ids: List[int]) -> Dict[str, Any]:
        """
        修改用户角色
        
        Args:
            user_id: 用户ID
            role_ids: 角色ID列表
            
        Returns:
            Dict: 修改结果
        """
        try:
            # 删除现有角色
            delete_sql =f"DELETE FROM user_roles WHERE user_id = {user_id}"
            db(None, None, delete_sql, 'mysql')

            # 添加新角色
            if role_ids:
                for role_id in role_ids:
                    insert_sql = f"INSERT INTO user_roles (user_id, role_id) VALUES ({user_id}, {role_id})"
                    db(None, None, insert_sql, 'mysql')
            
            return {'success': True, 'message': '修改用户角色成功'}
            
        except Exception as e:
            logger.error(f"修改用户角色失败: {e}")
            return {'success': False, 'message': f'修改用户角色失败: {str(e)}'}
    
    @staticmethod
    def check_permission(user_id: int, permission_name: str) -> Dict[str, Any]:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            permission_name: 权限名称
            
        Returns:
            Dict: 权限检查结果
        """
        try:
            sql = """
            SELECT COUNT(*) as count
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = :user_id AND p.name = :permission_name
            """
            result = select(sql, {
                'user_id': user_id,
                'permission_name': permission_name
            })
            
            has_permission = result[0]['count'] > 0 if result else False
            
            return {
                'success': True,
                'message': '权限检查完成',
                'data': {'has_permission': has_permission}
            }
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return {'success': False, 'message': f'权限检查失败: {str(e)}'}

    @staticmethod
    def test1_business(content:str):
        print(content)
        return  {'success': True, 'message': 'test1成功'}
# 全局用户业务实例
user_business = UserBusiness()
