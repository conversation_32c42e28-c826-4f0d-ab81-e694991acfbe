from datetime import datetime

from sqlalchemy import Column, DateTime, func, String, Integer, create_engine, select, update, delete
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.orm import declared_attr, declarative_base, sessionmaker, scoped_session

class BaseModel:
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()

    create_at=Column(DateTime,default=func.now(),comment="创建时间")
    update_at=Column(DateTime,default=func.now(),onupdate=func.now(),comment="更新时间")


#创建Base对象，作为其他模型依赖的基础
Base = declarative_base(cls=BaseModel)


#从db.mysql模块导入engine
from db.mysql import engine
#创建Session工厂
SessionLocal = sessionmaker(autocommit=False,autoflush=False,bind=engine)
#创建Session对象,线程安全的Session
Session = scoped_session(SessionLocal)
# 创建所有的表
def init_db():
    Base.metadata.create_all(bind=engine)





